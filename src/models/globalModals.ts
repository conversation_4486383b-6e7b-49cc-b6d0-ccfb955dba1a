import { useState } from 'react';

// 日策略弹框数据接口
export interface DayStrategyModalData {
  job_id: string;
  current_time?: string;
  target_job_id?: string;
  date?: string;
}

// 周策略弹框数据接口
export interface WeekStrategyModalData {
  job_id: string;
  current_time?: string;
  target_job_id?: string;
  date?: string;
  /** 是否是从"查看本周完整策略"按钮进入 */
  isCompleteStrategy?: boolean;
}

// 周和月报告弹框数据接口
export interface WeekMonthReportModalData {
  job_id: string;
  /** 必要参数 提交修改意见的时间要带上 */
  current_time?: string;
  target_job_id?: string;
}

// 日策略分类弹框数据接口
export interface DayStrategyCatModalData {
  job_id: string;
  current_time?: string;
  target_job_id?: string;
}

// 日策略弹框状态
interface DayStrategyModal {
  open: boolean;
  title: string;
  data?: DayStrategyModalData;
}

// 周策略弹框状态
interface WeekStrategyModal {
  open: boolean;
  title: string;
  data?: WeekStrategyModalData;
}

// 周报告弹框状态
interface WeekReportModal {
  open: boolean;
  title: string;
  data?: WeekMonthReportModalData;
}

// 月报告弹框状态
interface MonthReportModal {
  open: boolean;
  title: string;
  data?: WeekMonthReportModalData;
}

// 日策略分类弹框状态
interface DayStrategyCatModal {
  open: boolean;
  title: string;
  data?: DayStrategyCatModalData;
}

export default () => {
  // 日策略弹框状态
  const [dayStrategyModal, setDayStrategyModal] = useState<DayStrategyModal>({
    open: false,
    title: '',
    data: undefined,
  });

  // 周策略弹框状态
  const [weekStrategyModal, setWeekStrategyModal] = useState<WeekStrategyModal>({
    open: false,
    title: '',
    data: undefined,
  });

  // 周报告弹框状态
  const [weekReportModal, setWeekReportModal] = useState<WeekReportModal>({
    open: false,
    title: '',
    data: undefined,
  });

  // 月报告弹框状态
  const [monthReportModal, setMonthReportModal] = useState<MonthReportModal>({
    open: false,
    title: '',
    data: undefined,
  });

  // 日策略分类弹框状态
  const [dayStrategyCatModal, setDayStrategyCatModal] = useState<DayStrategyCatModal>({
    open: false,
    title: '',
    data: undefined,
  });

  // 打开日策略弹框
  const openDayStrategyModal = (data: DayStrategyModalData, title: string) => {
    setDayStrategyModal({
      open: true,
      title,
      data,
    });
  };

  // 关闭日策略弹框
  const closeDayStrategyModal = () => {
    setDayStrategyModal({
      open: false,
      title: '',
      data: undefined,
    });
  };

  // 打开周策略弹框
  const openWeekStrategyModal = (data: WeekStrategyModalData, title: string) => {
    setWeekStrategyModal({
      open: true,
      title,
      data,
    });
  };

  // 关闭周策略弹框
  const closeWeekStrategyModal = () => {
    setWeekStrategyModal({
      open: false,
      title: '',
      data: undefined,
    });
  };

  // 打开周报告弹框
  const openWeekReportModal = (data: WeekMonthReportModalData, title: string) => {
    setWeekReportModal({
      open: true,
      title,
      data,
    });
  };

  // 关闭周报告弹框
  const closeWeekReportModal = () => {
    setWeekReportModal({
      open: false,
      title: '',
      data: undefined,
    });
  };

  // 打开月报告弹框
  const openMonthReportModal = (data: WeekMonthReportModalData, title: string) => {
    setMonthReportModal({
      open: true,
      title,
      data,
    });
  };

  // 关闭月报告弹框
  const closeMonthReportModal = () => {
    setMonthReportModal({
      open: false,
      title: '',
      data: undefined,
    });
  };

  // 打开日策略分类弹框
  const openDayStrategyCatModal = (data: DayStrategyCatModalData, title: string) => {
    setDayStrategyCatModal({
      open: true,
      title,
      data,
    });
  };

  // 关闭日策略分类弹框
  const closeDayStrategyCatModal = () => {
    setDayStrategyCatModal({
      open: false,
      title: '',
      data: undefined,
    });
  };

  return {
    // 日策略弹框
    dayStrategyModal,
    openDayStrategyModal,
    closeDayStrategyModal,
    
    // 周策略弹框
    weekStrategyModal,
    openWeekStrategyModal,
    closeWeekStrategyModal,
    
    // 周报告弹框
    weekReportModal,
    openWeekReportModal,
    closeWeekReportModal,
    
    // 月报告弹框
    monthReportModal,
    openMonthReportModal,
    closeMonthReportModal,
    
    // 日策略分类弹框
    dayStrategyCatModal,
    openDayStrategyCatModal,
    closeDayStrategyCatModal,
  };
}; 