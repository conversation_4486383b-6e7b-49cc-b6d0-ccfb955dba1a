import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, Button, message, Space, Tag } from 'antd';
import { updateShop } from '@/services/ibidder_api/shop';
import { type ShopInfo } from '../../index';
import { countryName } from '@/utils/translationMaps';
import styles from './style.less';

interface SyncSettingsModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  shopInfo: ShopInfo | null;
  onReauthorize: (shop: ShopInfo) => void;
}

const SyncSettingsModal: React.FC<SyncSettingsModalProps> = ({ open, onCancel, onOk, shopInfo, onReauthorize }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (shopInfo) {
      form.setFieldsValue({
        sync_status: shopInfo.sync_status,
      });
    }
  }, [shopInfo, open]);

  const handleSave = async () => {
    if (!shopInfo) return;
    if (!shopInfo.ads_status) {
      onCancel();
      return;
    }
    try {
      const values = await form.validateFields();
      setLoading(true);
      const result: any = await updateShop({
        profile_id: shopInfo.profile_id,
        sync_status: values.sync_status,
      });
      if (result.code === 200) {
        message.success('保存成功');
        onOk();
      } else {
        message.error(result.message || '保存失败');
      }
    } catch (error) {
      console.error('保存同步设置出错:', error);
      message.error('操作异常');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAuth = async () => {
    if (!shopInfo) return;
    setLoading(true);
    try {
      const result: any = await updateShop({
        profile_id: shopInfo.profile_id,
        ads_status: false,
      });
      if (result.code === 200) {
        message.success('取消授权成功');
        onOk();
      } else {
        message.error(result.message || '操作失败');
      }
    } catch (error) {
      console.error('取消授权出错:', error);
      message.error('操作异常');
    } finally {
      setLoading(false);
    }
  };

  if (!shopInfo) return null;

  return (
    <Modal
      title="广告同步设置"
      open={open}
      onCancel={onCancel}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSave} style={{ backgroundColor: '#d93025', borderColor: '#d93025' }}>
          保存
        </Button>,
      ]}
      width={600}
      destroyOnClose
    >
      <Form form={form} layout="horizontal" style={{ marginTop: 24 }}>
        <Form.Item label="店铺名称：" className={styles.formItem}>
          <span>{shopInfo.shop_name}</span>
        </Form.Item>
        <Form.Item label="运营站点：" className={styles.formItem}>
          <span>{countryName(shopInfo.country_code)}</span>
        </Form.Item>

        <Form.Item label="广告授权：" required className={styles.formItem}>
          {shopInfo.ads_status ? (
            <Space>
              <Tag color="success">授权成功</Tag>
              <Button type="link" onClick={() => onReauthorize(shopInfo)}>重新授权</Button>
              <Button type="link" onClick={handleCancelAuth}>取消授权</Button>
            </Space>
          ) : (
            <Space>
              <Tag>未授权</Tag>
              <Button type="link" onClick={() => onReauthorize(shopInfo)}>去授权</Button>
            </Space>
          )}
        </Form.Item>

        {shopInfo.ads_status && (
          <Form.Item
            name="sync_status"
            label="广告数据："
            rules={[{ required: true, message: '请选择同步状态' }]}
            className={styles.formItem}
          >
            <Radio.Group disabled={!shopInfo.ads_status}>
              <Radio value={true}>同步</Radio>
              <Radio value={false}>暂停同步</Radio>
            </Radio.Group>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default SyncSettingsModal;
