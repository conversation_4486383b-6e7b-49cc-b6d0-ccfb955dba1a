import React, { useState, useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Table, Button, Select, Space, Dropdown, ConfigProvider, Modal, message, Input, Form, Flex, Spin } from 'antd';
import {
  PlusOutlined,
  MoreOutlined,
  ExclamationCircleFilled,
  EditOutlined,
} from '@ant-design/icons';
import AddShopModal from './components/AddShop';
import SyncSettingsModal from './components/SyncSettings';
import { getShopsList, getShopFilterOptions, updateShop, type ShopFilterOptions, getAuthResult, syncProfiles } from '@/services/ibidder_api/shop';
import styles from './index.less';
import { marketName, countryName } from '@/utils/translationMaps';
import { sourceImageUrl } from '@/utils/common';
import { useModel, history } from '@umijs/max';

// 定义店铺列表项和分组的接口
export interface ShopInfo {
  shop_name: string;
  profile_id: string;
  country_code: string;
  shop_status: boolean;
  ads_status: boolean;
  status: 'enabled' | 'disabled' | 'abnormal';
  sync_status: boolean;
  update_time: string;
  authorized_name: string;
  market?: string;
}

interface ShopItemGroup {
  market: string;
  seller_id: string;
  shop_list: ShopInfo[];
}

const ShopManagement: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [tableData, setTableData] = useState<ShopItemGroup[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState<React.Key[]>([]);
  const [editingShopId, setEditingShopId] = useState<string | null>(null);
  const [showNoAuthView, setShowNoAuthView] = useState<boolean>(false);
  const [editingShopInfo, setEditingShopInfo] = useState<any>(null);
  const [initialStep, setInitialStep] = useState<number>(1);
  const [isAuthorizing, setIsAuthorizing] = useState<boolean>(false);
  const [isSyncModalOpen, setIsSyncModalOpen] = useState(false);
  const [currentShop, setCurrentShop] = useState<ShopInfo | null>(null);

  // 添加筛选选项状态
  const [filterOptions, setFilterOptions] = useState<ShopFilterOptions>({
    market: [],
    shop: [],
    country_code: [],
    seller_id: []
  });

  // 添加筛选条件状态
  const [filters, setFilters] = useState<{
    market?: string;
    profile_id?: string;
    seller_id?: string;
    country_code?: string;
    shop_status?: string;
    ads_status?: string;
    shop_name?: string;
  }>({});

  // 分页状态
  const [pagination, setPagination] = useState<{
    current: number;
    pageSize: number;
    total: number;
  }>({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const [form] = Form.useForm();
  const isInitialLoad = useRef(true);
  const { initialState } = useModel('@@initialState');
  const { permissions } = initialState || {};
  const canViewShopAuth = permissions?.shop_auth_perm === 'MANAGE';
  // 获取筛选选项
  const fetchFilterOptions = async () => {
    try {
      const response: any = await getShopFilterOptions();
      if (response.code === 200) {
        const responseData = response.data;
        setFilterOptions({
          market: responseData.market || [],
          shop: responseData.shop || [],
          country_code: responseData.country_code || [],
          seller_id: responseData.seller_id || []
        });
      }
    } catch (error) {
      console.error('获取筛选选项出错:', error);
    }
  };

  // 获取列表数据
  const fetchShopsData = async (values?: any) => {
    setLoading(true);
    try {
      // 构建API参数，加入筛选条件
      const params: any = {
        page: pagination.current,
        page_size: pagination.pageSize
      };

      if (values) {
        if (values.market) params.market = values.market;
        if (values.profile_id) params.profile_id = values.profile_id;
        if (values.seller_id) params.seller_id = values.seller_id;
        if (values.country_code) params.country_code = values.country_code;
        if (values.shop_status !== undefined) params.shop_status = values.shop_status;
        if (values.ads_status !== undefined) params.ads_status = values.ads_status;
        if (values.shop_name) params.shop_name = values.shop_name.trim();
      } else {
        params.market = filters.market;
        params.profile_id = filters.profile_id;
        params.seller_id = filters.seller_id;
        params.country_code = filters.country_code;
        if (filters.shop_status !== undefined) params.shop_status = filters.shop_status;
        if (filters.ads_status !== undefined) params.ads_status = filters.ads_status;
        params.shop_name = filters.shop_name;
      }

      const response: any = await getShopsList(params);

      // 检查响应结构
      if (response.code === 200) {
        const responseData = response.data;

        // 仅在首次加载时，根据has_shop决定是否获取筛选选项
        if (isInitialLoad.current) {
          if (responseData.has_shop) {
            fetchFilterOptions();
          }
          isInitialLoad.current = false;
        }

        if (!response.data.has_shop) {
          if (canViewShopAuth) {
            setShowNoAuthView(true);
            setTableData([]); // 清空表格数据
            setPagination(prev => ({ ...prev, total: 0 })); // 重置分页
            return;
          } else {
            history.push('/authorization/unavailable');
            return;
          }
        } else {
          setShowNoAuthView(false);
        }
        const tableSource: ShopItemGroup[] = responseData.list;
        // 将父级的 market 注入到子级 shop_list 的每个项目中
        const processedData = tableSource.map(group => ({
          ...group,
          shop_list: group.shop_list.map(shop => ({
            ...shop,
            market: group.market,
          })),
        }));

        setTableData(processedData);

        // 默认展开所有分组
        const groupKeys = tableSource.map((item: ShopItemGroup) => item.seller_id);
        setExpandedRowKeys(groupKeys);

        // 更新分页信息
        setPagination(prev => ({
          ...prev,
          total: responseData.total
        }));
      }
    } catch (error) {
      console.error('获取店铺数据出错:', error);
      // 如果API调用失败，返回空数组
      setTableData([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSyncMarketClick = async (record: any) => {
    setLoading(true);
    try {
      const result: any = await syncProfiles({
        market: record.market,
        seller_id: record.seller_id,
      });
      if (result.code === 200) {
        message.success('站点同步成功');
        fetchShopsData();
      } else {
        message.error(result.message || '站点同步失败');
      }
    } catch (error) {
      console.error('站点同步失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理删除按钮点击
  const handleDeleteClick = (profile_id: string) => {
    Modal.confirm({
      title: '删除店铺',
      icon: <ExclamationCircleFilled />,
      content: '删除店铺后，店铺下相关数据将被彻底删除，不可恢复，请谨慎操作。',
      okText: '删除',
      cancelText: '取消',
      width: 400,
      async onOk() {
        try {
          const result: any = await updateShop({ profile_id, is_delete: true });
          if (result.code === 200) {
            message.success('删除成功');
            fetchShopsData();
          } else {
            message.error(result.message || '删除失败');
          }
        } catch (error) {
          console.error('删除操作出错:', error);
        }
      }
    });
  };

  // 处理更新店铺名称
  const handleUpdateShopName = async (profile_id: string, newName: string) => {
    if (!newName.trim()) {
      message.error('店铺名称不能为空');
      return;
    }
    try {
      const result: any = await updateShop({ profile_id, shop_name: newName.trim() });
      if (result.code === 200) {
        message.success('店铺名称更新成功');
        setEditingShopId(null);
        fetchShopsData();
      } else {
        message.error(result.message || '更新失败');
      }
    } catch (error) {
      console.error('更新操作出错:', error);
    }
  };

  // 处理店铺状态切换
  const handleToggleShopStatus = async (profile_id: string, currentStatus: 'enabled' | 'disabled') => {
    const newStatus = currentStatus === 'enabled' ? 'disabled' : 'enabled';
    if (newStatus === 'disabled') {
      Modal.confirm({
        title: '禁用店铺',
        icon: <ExclamationCircleFilled />,
        content: '禁用店铺后，店铺下相关数据不会显示在也页面上，且AI广告策略和执行操作也会停止，请谨慎操作。',
        okText: '禁用',
        cancelText: '取消',
        width: 400,
        async onOk() {
          try {
            const result: any = await updateShop({ profile_id, status: newStatus });
            if (result.code === 200) {
              message.success('禁用成功');
              fetchShopsData();
            } else {
              message.error(result.message || '禁用失败');
            }
          } catch (error) {
            console.error('禁用操作出错:', error);
          }
        }
      })
    } else {
      try {
        const result: any = await updateShop({ profile_id, status: newStatus });
        if (result.code === 200) {
          message.success('启用成功');
          fetchShopsData();
        } else {
          message.error(result.message || '启用失败');
        }
      } catch (error) {
        console.error('启用操作出错:', error);
      }
    }
  };

  // 处理授权操作
  const handleAuthorize = async (record: ShopInfo, authType: 'shop' | 'ads') => {
    if (!record.market) {
      message.error('缺少必要的 market 信息');
      return;
    }

    setEditingShopInfo({
      market: record.market,
      country_code: [record.country_code], // Modal内部需要数组
      shop_name: record.shop_name,
    });
    setInitialStep(2);
    setIsModalOpen(true);
  };

  // 渲染授权状态
  const renderAuthStatus = (status: boolean, record: ShopInfo, type: 'shop' | 'ads') => {
    if (status) {
      return (
        <div className={`${styles.authStatus} ${styles.authorized}`}>
          已授权
        </div>
      );
    }

    return (
      <div
        className={`${styles.authStatus} ${styles.unauthorized}`}
        onClick={() => handleAuthorize(record, type)}
      >
        立即授权
      </div>
    );
  };

  // 定义表格列
  const tableColumns = [
    {
      key: 'shop_name',
      title: '店铺名称',
      dataIndex: 'shop_name',
      width: 200,
      render: (text: string, record: any) => {
        if (record.shop_list) { // 判断是否为分组行
          return {
            children: (
              <div style={{ display: 'flex', alignItems: 'center', paddingLeft: 20, gap: 40 }}>
                <span>{marketName(record.market)}</span>
                <span>SellerID: {record.seller_id}</span>
              </div>
            ),
            props: {
              colSpan: 7, // 合并5列以容纳状态和同步状态
            },
          };
        }
        if (editingShopId === record.profile_id) {
          return (
            <Input
              defaultValue={text}
              autoFocus
              onPressEnter={(e) => handleUpdateShopName(record.profile_id, e.currentTarget.value)}
              onBlur={() => setEditingShopId(null)}
            />
          );
        }
        return (
          <Space>
            <span>{text}</span>
            <EditOutlined onClick={() => setEditingShopId(record.profile_id)} />
          </Space>
        );
      },
    },
    {
      key: 'country_code',
      title: '站点',
      dataIndex: 'country_code',
      width: 100,
      render: (text: string, record: any) => {
        if (record.shop_list) { // 判断是否为分组行
          return { props: { colSpan: 0 } };
        }
        return (
          <span>
            {countryName(text)}
          </span>
        );
      },
    },
    // {
    //   key: 'shop_auth_status',
    //   title: '店铺授权状态',
    //   width: 110,
    //   dataIndex: 'shop_status',
    //   render: (status: boolean, record: ShopInfo) => {
    //     if ((record as any).shop_list) {
    //       return { props: { colSpan: 0 } };
    //     }
    //     return renderAuthStatus(status, record, 'shop');
    //   }
    // },
    {
      key: 'ads_auth_status',
      title: '广告授权状态',
      width: 110,
      dataIndex: 'ads_status',
      render: (status: boolean, record: ShopInfo) => {
        if ((record as any).shop_list) {
          return { props: { colSpan: 0 } };
        }
        return renderAuthStatus(status, record, 'ads');
      }
    },
    {
      key: 'status',
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: (text: string, record: any) => {
        if (record.shop_list) {
          return { props: { colSpan: 0 } };
        }
        // if (!record.ads_status) return '-';
        return text === 'enabled' ? '启用' : text === 'disabled' ? '禁用' : '异常';
      }
    },
    {
      key: 'sync_status',
      title: '同步状态',
      dataIndex: 'sync_status',
      width: 100,
      render: (status: string, record: any) => {
        if (record.shop_list) {
          return { props: { colSpan: 0 } };
        }
        // if (!record.ads_status) return '-';
        return status ? '正常' : '暂停';
      }
    },
    {
      key: 'updated_at',
      title: '更新时间',
      dataIndex: 'update_time',
      width: 160,
      render: (text: string, record: any) => {
        if (record.shop_list) {
          return { props: { colSpan: 0 } };
        }
        return text || '-';
      }
    },
    {
      key: 'authorized_by',
      title: '授权人',
      dataIndex: 'authorized_name',
      width: 200,
      render: (text: string, record: any) => {
        if (record.shop_list) {
          return { props: { colSpan: 0 } };
        }
        return <span>{text || '-'}</span>;
      }
    },
    {
      key: 'action',
      title: '操作',
      align: 'center' as const,
      width: 80,
      render: (_: any, record: any) => {
        if (record.shop_list) { // 判断是否为分组行
          return {
            children: (
              <Dropdown menu={{
                items: [
                  {
                    key: 'sync',
                    label: '站点同步',
                    onClick: () => handleSyncMarketClick(record),
                  }
                ]
              }}>
                <Button type="text" size="small" onClick={e => e.preventDefault()}>
                  <MoreOutlined />
                </Button>
              </Dropdown>
            ),
            props: {
              colSpan: 1,
            },
          };
        }
        return (
          <Dropdown
            menu={{
              items: [
                {
                  key: 'ads-sync-settings',
                  label: '广告同步设置',
                  onClick: () => {
                    setCurrentShop(record);
                    setIsSyncModalOpen(true);
                  },
                },
                {
                  key: 'delete',
                  label: '删除店铺',
                  onClick: () => handleDeleteClick(record.profile_id),
                },
                {
                  key: 'toggle-status',
                  label: record.status === 'enabled' ? '禁用店铺' : '启用店铺',
                  onClick: () => handleToggleShopStatus(record.profile_id, record.status),
                },
              ],
            }}
          >
            <Button type="text" size="small" onClick={e => e.preventDefault()}>
              <MoreOutlined />
            </Button>
          </Dropdown>
        );
      },
    },
  ].filter(col => !['market', 'seller_id'].includes(col.key));

  // 处理添加店铺
  const handleAddShop = () => {
    setIsModalOpen(false);
    fetchShopsData();
  };

  // 处理分页变化
  const handleTableChange = (newPagination: any) => {
    setPagination(prev => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize
    }));
  };

  const handleAuthCallback = async (state: string) => {
    setIsAuthorizing(true);
    try {
      const result: any = await getAuthResult({ state });
      if (result.code === 200) {
        message.success('授权成功');
      } else {
        message.error(result.message || '授权失败');
      }
    } catch (error) {
      console.error('获取授权结果出错:', error);
    } finally {
      setIsAuthorizing(false);
      fetchShopsData();
      // 清理URL中的参数
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  };

  // 监听分页变化或初次加载，调用API
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const state = urlParams.get('state');
    if (state && isInitialLoad.current) {
      handleAuthCallback(state);
    } else {
      fetchShopsData();
    }
  }, [pagination.current, pagination.pageSize]);

  // 根据筛选条件搜索
  const handleSearch = (values: any) => {
    // 更新filters状态
    setFilters(values);

    // 重置分页到第一页
    if (pagination.current === 1) {
      fetchShopsData(values);
    } else {
      setPagination(prev => ({
        ...prev,
        current: 1
      }));
    }
  };

  const FilterArea = () => (
    <Form
      form={form}
      layout="inline"
      onValuesChange={(changedValues, allValues) => {
        // 只有非shop_name字段变化时才自动搜索
        if (Object.keys(changedValues).includes('shop_name')) {
          // 当清除输入框时，触发搜索
          if (changedValues.shop_name === '' || changedValues.shop_name === undefined) {
            handleSearch(allValues);
          }
          return;
        }
        handleSearch(allValues);
      }}
    >
      <Form.Item name="market" initialValue={filters.market}>
        <Select
          style={{ width: 120 }}
          placeholder="全部市场"
          allowClear
        >
          {filterOptions.market.map(market => (
            <Select.Option key={market} value={market}>{marketName(market)}</Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="profile_id" initialValue={filters.profile_id}>
        <Select
          style={{ width: 150 }}
          placeholder="全部店铺"
          allowClear
          showSearch
          filterOption={(input, option) =>
            (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
          }
        >
          {filterOptions.shop.map(shop => (
            <Select.Option key={shop.id} value={shop.id}>{shop.name}</Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="seller_id" initialValue={filters.seller_id}>
        <Select
          style={{ width: 150 }}
          placeholder="Seller ID"
          allowClear
          showSearch
          filterOption={(input, option) =>
            (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
          }
        >
          {filterOptions.seller_id.map(id => (
            <Select.Option key={id} value={id}>{id}</Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="country_code" initialValue={filters.country_code}>
        <Select
          style={{ width: 120 }}
          placeholder="全部国家"
          allowClear
        >
          {filterOptions.country_code.map(country => (
            <Select.Option key={country} value={country}>{countryName(country)}</Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="ads_status" initialValue={filters.ads_status}>
        <Select
          style={{ width: 140 }}
          placeholder="广告授权状态"
          allowClear
        >
          <Select.Option value={true}>已授权</Select.Option>
          <Select.Option value={false}>未授权</Select.Option>
        </Select>
      </Form.Item>

      {/* <Form.Item name="shop_status" initialValue={filters.shop_status}>
        <Select
          style={{ width: 140 }}
          placeholder="店铺授权状态"
          allowClear
        >
          <Select.Option value={true}>已授权</Select.Option>
          <Select.Option value={false}>未授权</Select.Option>
        </Select>
      </Form.Item> */}

      <Form.Item name="shop_name">
        <Input
          style={{ width: 200 }}
          placeholder="搜索店铺名称/卖家ID"
          allowClear
          autoComplete="off"
          onPressEnter={() => handleSearch(form.getFieldsValue())}
          onBlur={() => handleSearch(form.getFieldsValue())}
        />
      </Form.Item>
    </Form>
  );

  return (
    <div className={styles.shopManagement}>
      <PageContainer title={false} breadcrumbRender={false}>
        <Card style={showNoAuthView ? {border: 'none', background: 'transparent'} : {}}>
          {showNoAuthView ? (
            <div style={{ width: '100%', maxWidth: 1280, height: 'calc(100vh - 120px)', maxHeight: 666, display: 'flex', alignItems: 'center', background: '#fff', margin: '-24px auto' }}>
              <img src={sourceImageUrl('no_shop_auth.svg')} alt="No Auth" style={{ height: "100%" }} />
              <div style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', gap: 24 }}>
                <div style={{ fontSize: 20, color: '#1D2129', textAlign: 'center', lineHeight: 1.4 }}>在您开始使用FlyPower之前，<br />请先完成店铺广告授权</div>
                <div style={{ fontSize: 16, color: '#86909C', textAlign: 'center' }}>使用亚马逊官方授权接口，无关联风险</div>
                <Button type="primary" onClick={() => setIsModalOpen(true)} style={{ width: 370, height: 40, fontSize: 16, borderRadius: 20 }}>去授权</Button>
              </div>
            </div>
          ) : (
            <>
              <Flex justify='space-between' style={{marginBottom: 16}}>
                <FilterArea />
                <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>
                  添加店铺
                </Button>
              </Flex>
              <ConfigProvider renderEmpty={() =>
                <div style={{ textAlign: 'center', padding: '50px 0' }}>
                  <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>添加店铺</Button>
                  <p style={{ marginTop: 16 }}>暂无店铺，点击上方按钮，进行添加～</p>
                </div>
              }>
                <Table
                  columns={tableColumns as any}
                  dataSource={tableData}
                  loading={loading}
                  rowKey="seller_id"
                  sticky
                  onChange={handleTableChange}
                  rowClassName={(record) => {
                    if (record.shop_list) {
                      return styles['expanded-row-light-blue'];
                    }
                    return '';
                  }}
                  expandedRowKeys={expandedRowKeys}
                  onExpandedRowsChange={setExpandedRowKeys}
                  expandable={{
                    childrenColumnName: 'shop_list',
                    expandIcon: () => null,
                  }}
                  pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: pagination.total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `共 ${total} 条`,
                    pageSizeOptions: ['10', '20', '50', '100'],
                  }}
                />
              </ConfigProvider>
            </>
          )}
        </Card>
        <AddShopModal
          open={isModalOpen}
          onCancel={() => {
            setIsModalOpen(false);
            setEditingShopInfo(null);
            setInitialStep(1);
          }}
          onOk={handleAddShop}
          initialStep={initialStep}
          initialValues={editingShopInfo}
        />
        <SyncSettingsModal
          open={isSyncModalOpen}
          onCancel={() => {
            setIsSyncModalOpen(false);
            setCurrentShop(null);
          }}
          onOk={() => {
            setIsSyncModalOpen(false);
            setCurrentShop(null);
            fetchShopsData();
          }}
          shopInfo={currentShop}
          onReauthorize={(shop) => {
            setIsSyncModalOpen(false);
            handleAuthorize(shop, 'ads');
          }}
        />
        <Modal
          open={isAuthorizing}
          footer={null}
          closable={false}
          centered
          maskClosable={false}
          width={480}
        >
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: 450 }}>
            <Spin size="large" />
            <h2 style={{ marginTop: 24, fontSize: '20px' }}>授权中...</h2>
            <p style={{ color: '#333' }}>正在进行广告授权</p>
          </div>
        </Modal>
      </PageContainer>
    </div>
  );
};

export default ShopManagement;
