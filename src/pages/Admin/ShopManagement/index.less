.shopManagement {
  .authStatus {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 4px;

    
    &.authorized {
      color: #1DB88C;
    }
    
    &.unauthorized {
      color: #D80027;
      text-decoration: underline;
      text-decoration-style: dotted;
      cursor: pointer;
      
      &:hover {
        color: #f76565;
      }
    }
  }

  .operationButtons {
    .ant-btn {
      margin-right: 8px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.expanded-row-light-blue {
  background-color: #F7F9FF;
}
