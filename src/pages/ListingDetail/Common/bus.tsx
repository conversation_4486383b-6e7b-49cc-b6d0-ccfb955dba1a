import { Avatar } from "antd";
import { sourceImageUrl } from "@/utils/common";

/** 根据类型获取头像 */
export const getAvatarByType = (type: string) => {
  switch (type) {
    case 'marketAgent':
      return (
        <Avatar
          size={48}
          style={{ marginRight: '16px' }}
          src={sourceImageUrl('market_agent_img.png')}
        />
      );
    case 'strategyAgent':
      return (
        <Avatar
          size={48}
          style={{ marginRight: '16px' }}
          src={sourceImageUrl('strategy_agent_img.png')}
        />
      );
    case 'operAgent':
      return (
        <Avatar
          size={48}
          style={{ marginRight: '16px' }}
          src={sourceImageUrl('oper_agent_img.png')}
        />
      );
    default:
      return <Avatar size={48} style={{ marginRight: '16px' }} />;
  }
};

/** 根据广告类型获取颜色 */
export const getCampaignTypeColor = (text: string) => {
  let color = 'blue';
  if (text === 'sb') {
    color = 'red';
  } else if (text === 'sd') {
    color = 'green';
  }
  return color;
};