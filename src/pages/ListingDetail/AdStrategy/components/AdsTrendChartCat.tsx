import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { chartColors } from '@/utils/common';

type WeekAdsTrendChartProps = {
  currency: string;
  data: {
    xAxisData: string[];
    spend: number[];
    sales: number[];
    acos: number[];
    cvr: number[];
    orders: number[];
    clicks: number[];
    spend_totle?: number[];
    sales_totle?: number[];
    cvr_totle?: string[];
    acos_totle?: string[];
    orders_totle?: number[];
    clicks_totle?: number[];
  };
  chartOption?: {
    axisLabel: any
  }
};

/** 周或者日的广告趋势图 */
const AdsTrendChartCat: React.FC<WeekAdsTrendChartProps> = (props) => {
  const { currency, data } = props;
  const { xAxisData = [], spend: adSpendData = [], sales: adSalesData = [], acos: acosData = [], cvr: cvrData = [], orders: ordersData = [], clicks: clicksData = [], spend_totle = [], sales_totle = [], cvr_totle = [], acos_totle = [], orders_totle = [], clicks_totle = [] } = data;
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (chartRef.current) {
      const myChart = echarts.init(chartRef.current);

      const option: echarts.EChartsOption = {
        color: chartColors,
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.65)', // 黑色背景
          borderColor: '#444',
          textStyle: {
            color: '#fff', // 白色文字
          },
          formatter: (params: any) => {
            const { dataIndex } = params[0];
            let result = `<div style="color:#fff; margin-bottom: 8px;">${params[0].axisValue}</div>`;

            const containerStyle = `display: grid; grid-template-columns: auto auto auto auto auto auto; align-items: center; grid-gap:4px; color: #fff;`;
            let gridContent = '';

            const seriesData: Record<string, any> = {};
            params.forEach((p: any) => {
              seriesData[p.seriesName] = p;
            });

            const seriesOrder = ['广告花费', '广告销售额', 'ACOS', 'CVR', '订单量', '点击量'];

            seriesOrder.forEach(seriesName => {
              const param = seriesData[seriesName];
              if (!param) return;

              const { value, color } = param;
              let formattedValue;
              let totalLabel = '';
              let formattedTotalValue;

              if (seriesName === '广告花费') {
                formattedValue = `${currency}${value.toFixed(2)}`;
                totalLabel = '日累计:';
                formattedTotalValue = currency + spend_totle[dataIndex]
              } else if (seriesName === '广告销售额') {
                formattedValue = `${currency}${value.toFixed(2)}`;
                totalLabel = '日累计';
                formattedTotalValue = currency + sales_totle[dataIndex]
              } else if (seriesName === 'ACOS') {
                formattedValue = `${value.toFixed(2)}%`;
                totalLabel = '日均值';
                formattedTotalValue = acos_totle[dataIndex]
              } else if (seriesName === 'CVR') {
                formattedValue = `${value.toFixed(2)}%`;
                totalLabel = '日均值';
                formattedTotalValue = cvr_totle[dataIndex]
              } else if (seriesName === '订单量') {
                formattedValue = value;
                totalLabel = '日累计';
                formattedTotalValue = orders_totle[dataIndex]
              } else if (seriesName === '点击量') {
                formattedValue = value;
                totalLabel = '日累计';
                formattedTotalValue = clicks_totle[dataIndex]
              } else {
                formattedValue = value;
              }

              gridContent += `
                      <div><span style="color:${color};font-size:16px;">●</span> ${seriesName}</div>
                      <div>:</div>
                      <div style="text-align: right; font-weight: bold;">${formattedValue}</div>
                      <div style="margin-left: 16px;">${totalLabel}</div>
                      <div>:</div>
                      <div style="text-align: right; font-weight: bold;">${formattedTotalValue || ''}</div>
                  `;
            });

            result += `<div style="${containerStyle}">${gridContent}</div>`;
            return result;


          }
        },
        legend: {
          data: ['广告花费', '广告销售额', 'ACOS', 'CVR', '订单量', '点击量'],
          right: '2%',
          top: 'top',
        },
        grid: {
          left: '2%',
          right: '2%',
          bottom: '1%',
          top: 60,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: xAxisData,
          axisLabel: {
            ...props.chartOption?.axisLabel,
            hideOverlap: true,
            // 1. 使用 rich 来定义一个富文本样式
            rich: {
              // 'a' 是一个自定义的样式名，你可以随意命名
              a: {
                // 在这里直接设置行高
                lineHeight: 16
              }
            },
            // 2. 使用 formatter 将上面定义的样式应用到每个标签上
            //    '{a|{value}}' 的意思是：用名为 'a' 的样式来包裹标签的原始值(value)
            formatter: '{a|{value}}'
          }
        },
        yAxis: [
          {
            type: 'value',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
            },
          },
          {
            type: 'value',
            position: 'right',
            axisLabel: {
              formatter: '{value} %',
            },
            splitLine: {
              show: false, // 隐藏右侧y轴的横向指示线
            },
          },
        ],
        series: [
          {
            name: '广告花费',
            type: 'bar',
            yAxisIndex: 0,
            data: adSpendData,
            barMaxWidth: 30,
          },
          {
            name: '广告销售额',
            type: 'bar',
            yAxisIndex: 0,
            data: adSalesData,
            barMaxWidth: 30,
          },
          {
            name: 'ACOS',
            type: 'line',
            yAxisIndex: 1,
            data: acosData,
            // smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
            },
          },
          {
            name: 'CVR',
            type: 'line',
            yAxisIndex: 1,
            data: cvrData,
            // smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
            },
          },
          {
            name: '订单量',
            type: 'bar',
            yAxisIndex: 0,
            data: ordersData,
            barMaxWidth: 30,
          },
          {
            name: '点击量',
            type: 'bar',
            yAxisIndex: 0,
            data: clicksData,
            barMaxWidth: 30,
          },
        ],
      };

      myChart.setOption(option);

      const resizeHandler = () => {
        myChart.resize();
      };
      window.addEventListener('resize', resizeHandler);

      return () => {
        window.removeEventListener('resize', resizeHandler);
        myChart.dispose();
      };
    }
  }, [currency, data, props.chartOption]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

export default AdsTrendChartCat;