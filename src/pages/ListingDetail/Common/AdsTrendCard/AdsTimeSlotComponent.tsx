import React from 'react';
import { Typography, Empty } from 'antd';

const { Text } = Typography;

type AdsTimeSlotComponentProps = {
  type: 'day' | 'week' | 'month';
};

const AdsTimeSlotComponent: React.FC<AdsTimeSlotComponentProps> = ({ type }) => {
  return (
    <div style={{ padding: '40px 0', textAlign: 'center' }}>
      <Empty
        description={
          <div>
            <Text type="secondary">广告出单时段功能正在开发中</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              敬请期待...
            </Text>
          </div>
        }
      />
    </div>
  );
};

export default AdsTimeSlotComponent;
