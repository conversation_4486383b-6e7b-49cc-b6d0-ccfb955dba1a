import { Card, Descriptions, Tabs, Tag } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import styles from './index.less';
import SubAccountManagement from './components/SubAccountManagement';
import GroupManagement from './components/GroupManagement';
import RoleManagement from './components/RoleManagement';
import OperationLog from './components/OperationLog';
import { getTeamInfo, TeamInfoData } from '@/services/ibidder_api/user';
import { useModel, useSearchParams, useNavigate } from '@umijs/max';

const AccountManagement: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { permissions } = initialState || {};
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [teamInfo, setTeamInfo] = useState<TeamInfoData>();
  const [loading, setLoading] = useState(true);

  const fetchTeamInfo = () => {
    setLoading(true);
    getTeamInfo().then((res) => {
      if (res.code === 200) {
        setTeamInfo(res.data as any);
      }
    }).finally(() => {
      setLoading(false);
    }).catch((err) => {
      console.error('获取团队信息失败:', err);
    });
  };

  useEffect(() => {
    fetchTeamInfo();
  }, []);

  const teamInfoTitle = (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <span
        style={{
          backgroundColor: 'red',
          width: '4px',
          height: '16px',
          marginRight: '8px',
        }}
      />
      <span>团队信息</span>
    </div>
  );

  const allTabs = useMemo(
    () => [
      {
        key: '1',
        label: '子账号管理',
        component: SubAccountManagement,
        isPermitted: permissions?.account_management_perm === 'MANAGE',
      },
      // {
      //   key: '2',
      //   label: '分组管理',
      //   component: GroupManagement,
      //   isPermitted: permissions?.group_management_perm === 'MANAGE',
      // },
      {
        key: '3',
        label: '角色管理',
        component: RoleManagement,
        isPermitted: permissions?.role_management_perm === 'MANAGE',
      },
      {
        key: '4',
        label: '操作日志',
        component: OperationLog,
        isPermitted: permissions?.operation_log_perm !== 'NO_PERMISSION',
      },
    ],
    [permissions],
  );

  const availableTabs = useMemo(() => allTabs.filter((tab) => tab.isPermitted), [allTabs]);

  const activeKey = searchParams.get('tabKey') || availableTabs[0]?.key;

  const handleTabChange = (key: string) => {
    navigate(`/authorization/management?tabKey=${key}`);
  };

  return (
    <PageContainer
      header={{
        title: null,
      }}
      breadcrumbRender={false}
    >
      <div className={styles.container}>
        <Card loading={loading}>
          <Descriptions title={teamInfoTitle} column={3}>
            <Descriptions.Item label="公司名称">{teamInfo?.company_name || '-'}</Descriptions.Item>
            <Descriptions.Item label="可用子账户">
              {teamInfo?.used_sub_accounts || '-'} / {teamInfo?.total_sub_accounts || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="可添加Listing数">
              {teamInfo?.used_listings || '-'} / {teamInfo?.total_listings || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="账号有效期">{teamInfo?.valid_until || '-'}</Descriptions.Item>
            <Descriptions.Item label="账号状态">
              <Tag color={teamInfo?.account_status ? 'success' : 'default'}>
                {teamInfo?.account_status ? '正常' : '禁用'}
              </Tag>
            </Descriptions.Item>
          </Descriptions>
        </Card>
        {availableTabs.length > 0 && activeKey && (
          <Card style={{ marginTop: '24px' }}>
            <Tabs activeKey={activeKey} onChange={handleTabChange}>
              {availableTabs.map((tab) => {
                const Component = tab.component;
                const componentProps: any = {
                  isActive: activeKey === tab.key,
                };
                if (tab.key === '1') {
                  componentProps.teamInfo = teamInfo;
                  componentProps.onSubAccountAdd = fetchTeamInfo;
                }
                return (
                  <Tabs.TabPane tab={tab.label} key={tab.key}>
                    <Component {...componentProps} />
                  </Tabs.TabPane>
                );
              })}
            </Tabs>
          </Card>
        )}
      </div>
    </PageContainer>
  );
};

export default AccountManagement;
