// @ts-ignore
/* eslint-disable */
// API 更新时间：
// API 唯一标识：

import { request } from '@/request';
import { request as umiRequest } from '@umijs/max';

/**
 * 账号登录
 */
export async function accountLogin(params: API.AccountLoginParams) {
  return request('/api/v1/auth/login', {
    method: 'POST',
    data: params,
  });
}

/** 游客登录 POST /api/v1/auth/demo/login */
export async function guestLogin() {
  return request('/api/v1/auth/demo/login', {
    method: 'POST',
  });
}

// 发送动态验证码
export async function getDynamicCode(params: API.GetDynamicCodeParams) {
  return request('/api/v1/auth/dynamic-code', {
    method: 'POST',
    data: params,
  });
}

// 注册
export async function register(params: API.RegisterParams) {
  return request('/api/v1/auth/register', {
    method: 'POST',
    data: params,
  });
}

export interface GetRegistrationNotice {
    companyName: string;  // 公司名称
    companyType: string;  // 公司类型
    monthlyAdSpend: string;  // 预估月度广告支出
    name: string;  // 姓名
    phoneNumber: string;  // 电话号码
    workEmail: string;  // 工作邮箱
    [property: string]: any;
}
// 注册成功后获取注册通知 POST /api/v1/registration/notice
export async function getRegistrationNotice(params:GetRegistrationNotice) {
  return request('/api/v1/registration/notice', {
    method: 'POST',
    data: params,
  });
}

/** 获取消息列表 POST /api/v1/messages/list */
export async function getMessages(
  body: {
    /*0-未读 1-已读 */
    is_read?: number;
    /* 消息类型：1系统通知 2私信 3评论 4点赞 5任务提醒*/
    message_type?: number;
    page_no: number;
    page_size: number;
    parent_asin: string,
    profile_id: string,
  },
  options?: { [key: string]: any },
) {
  return request<API.MessageListResponse>('/api/v1/messages/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户设置信息 GET /api/v1/settings/info */
export async function getSettings() {
  return request<any>('/api/v1/settings/info', {
    method: 'GET',
  });
}

/** 更新用户设置 POST /api/v1/settings/update */
export async function updateSettings(params: any) {
  return request<any>('/api/v1/settings/update', {
    method: 'POST',
    data: params,
  });
}

// =========================用户管理=========================
export interface UserInfoResponse {
  code: number;
  data: UserInfoData;
  message: string;
  [property: string]: any;
}

export interface UserInfoData {
  company: string;
  email: null;
  is_main_account: boolean;
  phone: string;
  role_name: string;
  status: boolean;
  username: string;
  valid_until: string;
  [property: string]: any;
}
/** 获取用户基础信息 GET /api/v1/user/info */
export async function getUserInfo() {
  return request<UserInfoResponse>('/api/v1/user/info', {
    method: 'GET',
  });
}

export interface TeamInfoData {
  company_name: string;
  valid_until: string;
  used_sub_accounts: number;
  total_sub_accounts: number;
  account_status: boolean;
  used_listings: number;
  total_listings: number;
  [property: string]: any;
}
/** 获取团队信息 GET /api/v1/user/team_info */
export async function getTeamInfo() {
  return request<{
    code: number;
    data: TeamInfoData;
    message: string;
    [property: string]: any;
  }>('/api/v1/user/team_info', {
    method: 'GET',
  });
}

/** 获取用户权限信息 GET /api/v1/user/permissions */
export async function getUserPermissions() {
  return request<{
    code: number;
    data: API.Permissions;
    message: string;
    [property: string]: any;
  }>('/api/v1/user/permissions', {
    method: 'GET',
  });
}

export interface SubAccountResponse {
  code: number;
  data: SubAccountData;
  message: string;
  [property: string]: any;
}

export interface SubAccountData {
  items: SubAccountItem[];
  total: number;
  max_sub_accounts: number;
  added_sub_accounts: number;
  [property: string]: any;
}

export interface SubAccountItem {
  is_main?: boolean;
  asin_permission_count?: number;
  can_edit?: boolean;
  email?: string;
  id?: number;
  last_login_time?: string;
  created_at?: string;
  login_count?: number;
  phone?: null;
  role_name?: string;
  shop_permission_count?: number;
  status?: boolean;
  user_name?: string;
  [property: string]: any;
}
export interface SubAccountRequest {
  page: number;
  page_size: number;
  role_id?: number;
  status?: boolean;
  username?: string;
  [property: string]: any;
}
/** 获取子账户列表 POST /api/v1/user/account/list */
export async function getSubAccounts(data: SubAccountRequest) {
  return request<SubAccountResponse>('/api/v1/user/account/list', {
    method: 'POST',
    data,
  });
}


export interface AccountFilterOptionsResponse {
  code: number;
  data: {
    role_map: {
      role_id: number;
      role_name: string;
      role_type: number;
    }[];
  };
  message: string;
  [property: string]: any;
}

/** 获取子账户筛选选项 GET /api/v1/user/account/filter_options */
  export async function getAccountFilterOptions() {
    return request<AccountFilterOptionsResponse>('/api/v1/user/account/filter_options', {
      method: 'GET',
    });
  }

/** 禁用子账户 POST /api/v1/user/account/update_status */
export async function updateSubAccountStatus(params: { user_id: number, status: boolean }) {
  return request<any>('/api/v1/user/account/update_status', {
    method: 'POST',
    data: params,
  });
}

/** 获取店铺列表 GET /api/v1/user/profile/list */
export async function getShopList(params: {
  user_id?: number;
}) {
  return request<{
    code: number;
    data: {
      profile_id_list: {
        profile_id: string;
        name: string;
      }[];
      selected: string[];
    };
    message: string;
    [property: string]: any;
  }>('/api/v1/user/profile/list', {
    method: 'GET',
    params,
  });
}

/** 添加子账号 POST /api/v1/user/account/create_account */
export async function createSubAccount(params: {
  user_name: string;
  role_id: number;
  email: string;
  phone?: string;
  password: string;
  group_id?: number;
  profile_id_list: string[];
  [property: string]: any;
}) {
  return request<any>('/api/v1/user/account/create_account', {
    method: 'POST',
    data: params,
  });
}

/** 编辑子账号 POST /api/v1/user/account/edit_account */
export async function editSubAccount(params: {
  user_id: number;
  user_name: string;
  role_id: number;
  email: string;
  phone?: string;
  group_id?: number;
  profile_id_list: string[];
  [property: string]: any;
}) {
  return request<any>('/api/v1/user/account/edit_account', {
    method: 'POST',
    data: params,
  });
}

/** 管理员修改子账号密码 POST /api/v1/user/account/admin_update_password */
export async function adminUpdatePassword(params: {
  user_id: number;
  password: string;
}) {
  return request<any>('/api/v1/user/account/admin_update_password', {
    method: 'POST',
    data: params,
  });
}

/** 账号修改自己密码 POST /api/v1/user/account/update_password */
export async function updatePassword(params: {
  old_password: string;
  new_password: string;
}) {
  return request<any>('/api/v1/user/account/update_password', {
    method: 'POST',
    data: params,
  });
}

// =========================角色管理=========================
/** 获取角色列表 GET /api/v1/user/role/list */
export interface RoleItem {
  id: number;
  name: string;
  type: number;
  account_management_perm: 'MANAGE' | 'NO_PERMISSION';
  role_management_perm: 'MANAGE' | 'NO_PERMISSION';
  shop_auth_perm: 'MANAGE' | 'NO_PERMISSION';
  group_management_perm: 'MANAGE' | 'NO_PERMISSION';
  operation_log_perm: 'MANAGE' | 'VIEW' | 'NO_PERMISSION';
  ai_switch_perm: 'MANAGE' | 'NO_PERMISSION';
  [property: string]: any;
}
export async function getRoleList() {
  return request<{
    code: number;
    data: {
      items: RoleItem[];
    };
    message: string;
    [property: string]: any;
  }>('/api/v1/user/role/list', {
    method: 'GET',
  });
}

/** 添加角色 POST /api/v1/user/role/create_role */
export async function addRole(params: {
  name: string;
}) {
  return request<any>('/api/v1/user/role/create_role', {
    method: 'POST',
    data: params,
  });
}

/** 修改角色信息 POST /api/v1/user/role/update_role */
export async function updateRole(params: {
  id: number;
  name?: string;
  shop_auth_perm?: string;
  group_management_perm?: string;
  operation_log_perm?: string;
  ai_switch_perm?: string;
}) {
  return request<any>('/api/v1/user/role/update_role', {
    method: 'POST',
    data: params,
  });
}

/** 删除角色 POST /api/v1/user/role/delete_role */
export async function deleteRole(params: {
  id: number;
}) {
  return request<any>('/api/v1/user/role/delete_role', {
    method: 'POST',
    data: params,
  });
}

//=====================操作日志=====================
/** 获取操作日志 POST /api/v1/user/operation_log/list */
export interface OperationLogItem {
  id: number;
  user_id: number;
  operation_type: string;
  operation_type_text: string;
  operation_detail: string;
  operation_time: string;
  created_at: string;
  [property: string]: any;
}
export async function getOperationLog(params: {
  page: number;
  page_size: number;
  user_id?: number;
  start_time?: string;
  end_time?: string;
  [property: string]: any;
}) {
  return request<{
    code: number;
    data: {
      list: OperationLogItem[];
      total: number;
    };
    message: string;
    [property: string]: any;
  }>('/api/v1/user/operation_log/list', {
    method: 'POST',
    data: params,
  });
}

/** 操作日志筛选选项 GET /api/v1/user/operation_log/filter_options */
export async function getOperationLogFilterOptions() {
  return request<{
    code: number;
    data: {
      user_map: {
        user_id: number;
        user_name: string;
      }[];
    };
    message: string;
    [property: string]: any;
  }>('/api/v1/user/operation_log/filter_options', {
    method: 'GET',
  });
}
