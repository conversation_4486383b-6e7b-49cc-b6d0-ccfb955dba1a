import React, { useState, useEffect } from 'react';
import { Table, Space, Row, Col, Spin, Tag, Radio } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import 'dayjs/locale/en';
import type { Dayjs } from 'dayjs';
import PresetRangePicker from './components/PresetRangePicker';
import ReusableChart from './ReusableChart';
import { getCampaignPlacement } from '@/services/ibidder_api/operation';
import type { CampaignPlacementResponse } from './types';
import { getWeekday, countryTimezoneMap } from '@/utils/bus';
import { createTitleWithTooltip } from './utils/tooltipConfig';

const adPlacementSeriesConfig = [
  { name: '广告订单', key: 'orders', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '花费', key: 'spend', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '销售额', key: 'sales', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: 'ACoS', key: 'acos', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CVR', key: 'cvr', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CTR', key: 'ctr', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: '曝光量', key: 'impressions', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '点击', key: 'clicks', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '点击百分比', key: 'clicks_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CPC', key: 'cpc', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: 'CPA', key: 'cpa', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '广告销量', key: 'adSales', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '直接成交订单', key: 'sku_orders', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '直接成交销售额', key: 'sku_sales', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '花费百分比', key: 'spend_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: '销售额百分比', key: 'sales_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
];

interface AdPlacementTabProps {
  campaignId: string | number;
  campaign_type: string;
}

interface PlacementDataType {
  key: React.Key;
  placement: string;
  impressions: number;
  clicks: number;
  clicks_ratio: string;
  ctr: string;
  cpc: string;
  spend: string;
  sales: string;
  acos: string;
  orders: number;
  sku_orders: number;
  sku_sales: string;
  cpa: string;
  cvr: string;
  units: number;
  spend_ratio: string;
  sales_ratio: string;
}

interface DailyDataType {
  key: React.Key;
  date: string;
  impressions: number;
  clicks: number;
  clicks_ratio: string;
  ctr: string;
  cpc: string;
  spend: string;
  sales: string;
  acos: string;
  orders: number;
  sku_orders: number;
  sku_sales: string;
  cpa: string;
  cvr: string;
  units: number;
  spend_ratio: string;
  sales_ratio: string;
}

const AdPlacementTab: React.FC<AdPlacementTabProps> = ({ campaignId, campaign_type }) => {
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '';
  const profileId = productInfo?.profile_id;
  const country = (productInfo?.country || '').toLowerCase();
  const tz = countryTimezoneMap[country as keyof typeof countryTimezoneMap];
  const now = tz ? dayjs().tz(tz) : dayjs();
  const [loading, setLoading] = useState(false);
  const [chartLoading, setChartLoading] = useState(false);
  const [activePlacement, setActivePlacement] = useState('Top of Search on-Amazon');
  const [dateRange, setDateRange] = useState<[string, string]>([
    now.clone().subtract(13, 'days').format('YYYY-MM-DD'),
    now.clone().format('YYYY-MM-DD'),
  ]);
  const [dateRangeValue, setDateRangeValue] = useState<[Dayjs, Dayjs]>([
    now.clone().subtract(13, 'days'),
    now.clone(),
  ]);
  const [selectedSeries, setSelectedSeries] = useState<string[]>(() => adPlacementSeriesConfig.slice(0, 6).map(s => s.name));
  const [dailyTableData, setDailyTableData] = useState<DailyDataType[]>([]);
  const [chartData, setChartData] = useState<any>(null);
  const [placementData, setPlacementData] = useState<PlacementDataType[]>([]);
  const [apiData, setApiData] = useState<CampaignPlacementResponse | null>(null);

  const placementColumns: ColumnsType<PlacementDataType> = [
    {
      title: '广告位', dataIndex: 'placement', key: 'placement', width: 160, fixed: 'left',
      render: (value: string) => {
        let name = ''
        switch (value) {
          case 'Top of Search on-Amazon':
            name = '搜索结果顶部(首页)'
            break;
          case 'Other on-Amazon':
            name = '搜索结果其余位置'
            break;
          case 'Detail Page on-Amazon':
            name = '商品页面'
            break;
          default:
            name = value
            break;
        }
        return name
      }
    },
    { title: createTitleWithTooltip('曝光量', 'impressions'), dataIndex: 'impressions', key: 'impressions', width: 118, align: 'right' },
    { title: createTitleWithTooltip('点击', 'clicks'), dataIndex: 'clicks', key: 'clicks', width: 118, align: 'right' },
    { title: createTitleWithTooltip('点击百分比', 'click_ratio'), dataIndex: 'clicks_ratio', key: 'clicks_ratio', width: 128, align: 'right' },
    { title: createTitleWithTooltip('CTR', 'ctr'), dataIndex: 'ctr', key: 'ctr', width: 118, align: 'right' },
    { title: createTitleWithTooltip('CPC', 'cpc'), dataIndex: 'cpc', key: 'cpc', width: 118, align: 'right' },
    { title: createTitleWithTooltip('花费', 'spend'), dataIndex: 'spend', key: 'spend', width: 118, align: 'right' },
    { title: createTitleWithTooltip('花费百分比', 'spend_ratio'), dataIndex: 'spend_ratio', key: 'spend_ratio', width: 128, align: 'right' },
    { title: createTitleWithTooltip('销售额', 'sales'), dataIndex: 'sales', key: 'sales', width: 128, align: 'right' },
    { title: createTitleWithTooltip('销售额百分比', 'sales_ratio'), dataIndex: 'sales_ratio', key: 'sales_ratio', width: 138, align: 'right' },
    { title: createTitleWithTooltip('直接成交销售额', 'sku_sales'), dataIndex: 'sku_sales', key: 'sku_sales', width: 158, align: 'right' },
    { title: createTitleWithTooltip('ACoS', 'acos'), dataIndex: 'acos', key: 'acos', width: 118, align: 'right' },
    { title: createTitleWithTooltip('广告订单', 'orders'), dataIndex: 'orders', key: 'orders', width: 118, align: 'right' },
    { title: createTitleWithTooltip('直接成交订单', 'sku_orders'), dataIndex: 'sku_orders', key: 'sku_orders', width: 138, align: 'right' },
    { title: createTitleWithTooltip('CPA', 'cpa'), dataIndex: 'cpa', key: 'cpa', width: 118, align: 'right' },
    { title: createTitleWithTooltip('CVR', 'cvr'), dataIndex: 'cvr', key: 'cvr', width: 118, align: 'right' },
    { title: createTitleWithTooltip('广告销量', 'units'), dataIndex: 'units', key: 'units', width: 118, align: 'right' },
  ];

  const dailyColumns: ColumnsType<DailyDataType> = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 180,
      fixed: 'left',
      render: (value) => {
        const date = dayjs(value);
        const weekdayEng = date.locale('en').format('ddd');
        const weekday = getWeekday(weekdayEng);
        if (weekday) {
          // 判断是否为周末（周六、周日）
          const isWeekend = weekdayEng === 'Sat' || weekdayEng === 'Sun';
          return (
            <Space>
              <Tag color={isWeekend ? 'orange' : 'blue'}>{weekday}</Tag>
              <span>{value}</span>
            </Space>
          );
        }
        return value;
      },
      sorter: (a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf(),
      defaultSortOrder: 'descend' as const,
    },
    { title: createTitleWithTooltip('曝光量', 'impressions'), dataIndex: 'impressions', key: 'impressions', width: 118, sorter: (a, b) => a.impressions - b.impressions, align: 'right' },
    { title: createTitleWithTooltip('点击', 'clicks'), dataIndex: 'clicks', key: 'clicks', width: 118, sorter: (a, b) => a.clicks - b.clicks, align: 'right' },
    { title: createTitleWithTooltip('点击百分比', 'click_ratio'), dataIndex: 'clicks_ratio', key: 'clicks_ratio', width: 138, sorter: (a, b) => parseFloat(a.clicks_ratio) - parseFloat(b.clicks_ratio), align: 'right' },
    { title: createTitleWithTooltip('CTR', 'ctr'), dataIndex: 'ctr', key: 'ctr', width: 118, sorter: (a, b) => parseFloat(a.ctr) - parseFloat(b.ctr), align: 'right' },
    { title: createTitleWithTooltip('CPC', 'cpc'), dataIndex: 'cpc', key: 'cpc', width: 118, sorter: (a, b) => parseFloat(a.cpc.replace(/[^0-9.-]+/g, '')) - parseFloat(b.cpc.replace(/[^0-9.-]+/g, '')), align: 'right' },
    { title: createTitleWithTooltip('花费', 'spend'), dataIndex: 'spend', key: 'spend', width: 118, sorter: (a, b) => parseFloat(a.spend.replace(/[^0-9.-]+/g, '')) - parseFloat(b.spend.replace(/[^0-9.-]+/g, '')), align: 'right' },
    { title: createTitleWithTooltip('花费百分比', 'spend_ratio'), dataIndex: 'spend_ratio', key: 'spend_ratio', width: 138, sorter: (a, b) => parseFloat(a.spend_ratio) - parseFloat(b.spend_ratio), align: 'right' },
    { title: createTitleWithTooltip('销售额', 'sales'), dataIndex: 'sales', key: 'sales', width: 138, sorter: (a, b) => parseFloat(a.sales.replace(/[^0-9.-]+/g, '')) - parseFloat(b.sales.replace(/[^0-9.-]+/g, '')), align: 'right' },
    { title: createTitleWithTooltip('销售额百分比', 'sales_ratio'), dataIndex: 'sales_ratio', key: 'sales_ratio', width: 158, sorter: (a, b) => parseFloat(a.sales_ratio) - parseFloat(b.sales_ratio), align: 'right' },
    { title: createTitleWithTooltip('直接成交销售额', 'sku_sales'), dataIndex: 'sku_sales', key: 'sku_sales', width: 178, sorter: (a, b) => parseFloat(a.sku_sales.replace(/[^0-9.-]+/g, '')) - parseFloat(b.sku_sales.replace(/[^0-9.-]+/g, '')), align: 'right' },
    { title: createTitleWithTooltip('ACoS', 'acos'), dataIndex: 'acos', key: 'acos', width: 118, sorter: (a, b) => parseFloat(a.acos) - parseFloat(b.acos), align: 'right' },
    { title: createTitleWithTooltip('广告订单', 'orders'), dataIndex: 'orders', key: 'orders', width: 128, sorter: (a, b) => a.orders - b.orders, align: 'right' },
    { title: createTitleWithTooltip('直接成交订单', 'sku_orders'), dataIndex: 'sku_orders', key: 'sku_orders', width: 158, sorter: (a, b) => a.sku_orders - b.sku_orders, align: 'right' },
    { title: createTitleWithTooltip('CPA', 'cpa'), dataIndex: 'cpa', key: 'cpa', width: 118, sorter: (a, b) => parseFloat(a.cpa.replace(/[^0-9.-]+/g, '')) - parseFloat(b.cpa.replace(/[^0-9.-]+/g, '')), align: 'right' },
    { title: createTitleWithTooltip('CVR', 'cvr'), dataIndex: 'cvr', key: 'cvr', width: 118, sorter: (a, b) => parseFloat(a.cvr) - parseFloat(b.cvr), align: 'right' },
    { title: createTitleWithTooltip('广告销量', 'units'), dataIndex: 'units', key: 'units', width: 128, sorter: (a, b) => a.units - b.units, align: 'right' },
  ];

  // Helper function to format currency values
  const formatCurrency = (value: number | string) => {
    if (value === null || value === undefined) return '-';
    if (typeof value === 'string') return value;
    return `${currency}${value.toFixed(2)}`;
  };

  // Helper function to format percentage values
  const formatPercentage = (value: number | string) => {
    if (value === null || value === undefined) return '-';
    if (typeof value === 'string') return value;
    return `${(value * 100).toFixed(2)}%`;
  };

  // Transform API data to table format
  const transformPlacementData = (data: CampaignPlacementResponse): PlacementDataType[] => {
    const result: PlacementDataType[] = data.Summary?.list?.map((item) => ({
      key: item.placement,
      placement: item.placement,
      impressions: item.impressions,
      clicks: item.clicks,
      clicks_ratio: formatPercentage(item.clicks_ratio),
      ctr: formatPercentage(item.ctr),
      cpc: formatCurrency(item.cpc),
      spend: formatCurrency(item.spend),
      spend_ratio: formatPercentage(item.spend_ratio),
      sales: formatCurrency(item.sales),
      sales_ratio: formatPercentage(item.sales_ratio),
      sku_sales: formatCurrency(item.sku_sales),
      acos: formatPercentage(item.acos),
      orders: item.orders,
      sku_orders: item.sku_orders,
      cpa: formatCurrency(item.cpa),
      cvr: formatPercentage(item.cvr),
      units: item.units,
    }));

    return result;
  };

  // Transform daily data for the selected placement
  const transformDailyData = (data: CampaignPlacementResponse, placement: string): DailyDataType[] => {
    const result: DailyDataType[] = [];
    let placementData;

    switch (placement) {
      case 'Top of Search on-Amazon':
        placementData = data['Top of Search on-Amazon'];
        break;
      case 'Other on-Amazon':
        placementData = data['Other on-Amazon'];
        break;
      case 'Detail Page on-Amazon':
        placementData = data['Detail Page on-Amazon'];
        break;
      default:
        placementData = data.Summary;
    }

    if (!placementData) return result;

    // Add daily data
    placementData.list?.forEach((item, index) => {
      result.push({
        key: `daily-${index}`,
        date: (item as any).date || `Day ${index + 1}`,
        impressions: item.impressions,
        clicks: item.clicks,
        clicks_ratio: formatPercentage(item.clicks_ratio),
        ctr: formatPercentage(item.ctr),
        cpc: formatCurrency(item.cpc),
        spend: formatCurrency(item.spend),
        spend_ratio: formatPercentage(item.spend_ratio),
        sales: formatCurrency(item.sales),
        sales_ratio: formatPercentage(item.sales_ratio),
        sku_sales: formatCurrency(item.sku_sales),
        acos: formatPercentage(item.acos),
        orders: item.orders,
        sku_orders: item.sku_orders,
        cpa: formatCurrency(item.cpa),
        cvr: formatPercentage(item.cvr),
        units: item.units,
      });
    });

    return result;
  };

  // Transform chart data for the selected placement
  const transformChartData = (data: CampaignPlacementResponse, placement: string) => {
    let placementData;

    switch (placement) {
      case 'Top of Search on-Amazon':
        placementData = data['Top of Search on-Amazon'];
        break;
      case 'Other on-Amazon':
        placementData = data['Other on-Amazon'];
        break;
      case 'Detail Page on-Amazon':
        placementData = data['Detail Page on-Amazon'];
        break;
      default:
        placementData = data.Summary;
    }

    if (!placementData?.list) return null;

    const list = placementData.list;

    return {
      dates: list.map(item => (item as any).date || ''),
      impressions: list.map(item => item.impressions),
      clicks: list.map(item => item.clicks),
      clicks_ratio: list.map(item => typeof item.clicks_ratio === 'number' ? item.clicks_ratio * 100 : 0),
      ctr: list.map(item => typeof item.ctr === 'number' ? item.ctr * 100 : 0),
      cpc: list.map(item => typeof item.cpc === 'number' ? item.cpc : 0),
      spend: list.map(item => typeof item.spend === 'number' ? item.spend : 0),
      spend_ratio: list.map(item => typeof item.spend_ratio === 'number' ? item.spend_ratio * 100 : 0),
      sales: list.map(item => typeof item.sales === 'number' ? item.sales : 0),
      sales_ratio: list.map(item => typeof item.sales_ratio === 'number' ? item.sales_ratio * 100 : 0),
      sku_sales: list.map(item => item.sku_sales),
      acos: list.map(item => typeof item.acos === 'number' ? item.acos * 100 : 0),
      orders: list.map(item => item.orders),
      sku_orders: list.map(item => item.sku_orders),
      cpa: list.map(item => typeof item.cpa === 'number' ? item.cpa : 0),
      cvr: list.map(item => typeof item.cvr === 'number' ? item.cvr * 100 : 0),
      adSales: list.map(item => item.units),
    };
  };

  useEffect(() => {
    const fetchPlacementData = async () => {
      console.log('AdPlacementTab - campaignId:', campaignId, 'profileId:', profileId, 'campaign_type:', campaign_type);
      if (!campaignId || !profileId) {
        console.log('AdPlacementTab - Skipping API call due to missing params');
        return;
      }

      setLoading(true);
      try {
        const response = await getCampaignPlacement({
          campaign_id: Number(campaignId),
          campaign_type: campaign_type as 'sp' | 'sd',
          start_date: dateRange[0],
          end_date: dateRange[1],
          profile_id: profileId,
        });

        setApiData(response.data);
      } catch (error) {
        console.error('Failed to fetch placement data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPlacementData();
  }, [campaignId, campaign_type, dateRange, profileId]);

  useEffect(() => {
    if (!apiData) return;

    setChartLoading(true);
    try {
      setPlacementData(transformPlacementData(apiData));
      const dailyData = transformDailyData(apiData, activePlacement);
      const chartData = transformChartData(apiData, activePlacement);

      setDailyTableData(dailyData);
      setChartData(chartData);
    } catch (error) {
      console.error('Failed to transform data:', error);
    } finally {
      setChartLoading(false);
    }
  }, [apiData, activePlacement]);

  const handleDateChange = (
    dates: null | (Dayjs | null)[] | any,
    dateStrings: [string, string],
  ) => {
    if (dates && dates[0] && dates[1] && dateStrings[0] && dateStrings[1]) {
      setDateRangeValue([dates[0], dates[1]]);
      setDateRange(dateStrings);
    }
  };

  const handleSelectedSeriesChange = (newSelectedSeries: string[]) => {
    setSelectedSeries(newSelectedSeries);
  };

  const handlePlacementChange = (placement: string) => {
    setActivePlacement(placement);
  };

  return (
    <Spin spinning={loading}>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <div>
          <PresetRangePicker value={dateRangeValue} onChange={handleDateChange} style={{ marginBottom: 16 }} />
          <Table
            sticky
            columns={placementColumns}
            dataSource={placementData}
            pagination={false}
            scroll={{ x: 'max-content' }}
            summary={() => {
              if (!apiData?.Summary?.total) return null;
              const total = apiData.Summary.total;
              return (
                <Table.Summary fixed="top">
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0}>汇总</Table.Summary.Cell>
                    <Table.Summary.Cell index={1} align="right">{total.impressions}</Table.Summary.Cell>
                    <Table.Summary.Cell index={2} align="right">{total.clicks}</Table.Summary.Cell>
                    <Table.Summary.Cell index={3} align="right">{formatPercentage(total.clicks_ratio)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={4} align="right">{formatPercentage(total.ctr)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={5} align="right">{formatCurrency(total.cpc)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={6} align="right">{formatCurrency(total.spend)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={7} align="right">{formatPercentage(total.spend_ratio)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={8} align="right">{formatCurrency(total.sales)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={9} align="right">{formatPercentage(total.sales_ratio)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={10} align="right">{formatCurrency(total.sku_sales)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={11} align="right">{formatPercentage(total.acos)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={12} align="right">{total.orders}</Table.Summary.Cell>
                    <Table.Summary.Cell index={13} align="right">{total.sku_orders}</Table.Summary.Cell>
                    <Table.Summary.Cell index={14} align="right">{formatCurrency(total.cpa)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={15} align="right">{formatPercentage(total.cvr)}</Table.Summary.Cell>
                    <Table.Summary.Cell index={16} align="right">{total.units}</Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              );
            }}
          />
        </div>

        <Spin spinning={chartLoading}>
          <div>
            <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
              <Col>
                <Space>
                  <PresetRangePicker value={dateRangeValue} onChange={handleDateChange} />
                  <Radio.Group value={activePlacement} onChange={(e) => handlePlacementChange(e.target.value)}>
                    <Radio.Button value="Top of Search on-Amazon">搜索结果顶部</Radio.Button>
                    <Radio.Button value="Other on-Amazon">搜索结果其他位置</Radio.Button>
                    <Radio.Button value="Detail Page on-Amazon">商品页面</Radio.Button>
                  </Radio.Group>
                </Space>
              </Col>
            </Row>
            {chartData ? (
              <div style={{ height: 300, marginBottom: 16 }}>
                <ReusableChart
                  currency={currency}
                  dates={chartData.dates}
                  series={adPlacementSeriesConfig.map(config => ({
                    ...config,
                    data: chartData[config.key]
                  }))}
                  currencyFormattedSeries={['花费', '销售额', 'CPC', 'CPA', '直接成交销售额']}
                  percentageFormattedSeries={['点击百分比', 'CTR', '花费百分比', '销售额百分比', 'ACoS', 'CVR']}
                  selectedSeries={selectedSeries}
                  onSelectedSeriesChange={handleSelectedSeriesChange}
                />
              </div>
            ) : null}
            <Table
              sticky
              columns={dailyColumns}
              dataSource={dailyTableData}
              pagination={{
                total: dailyTableData.length,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条`,
              }}
              scroll={{ x: 'max-content' }}
              summary={() => {
                if (!apiData) return null;
                let placementData;
                switch (activePlacement) {
                  case 'Top of Search on-Amazon':
                    placementData = apiData['Top of Search on-Amazon'];
                    break;
                  case 'Other on-Amazon':
                    placementData = apiData['Other on-Amazon'];
                    break;
                  case 'Detail Page on-Amazon':
                    placementData = apiData['Detail Page on-Amazon'];
                    break;
                  default:
                    placementData = apiData.Summary;
                }

                if (!placementData?.total) return null;
                const total = placementData.total;

                return (
                  <Table.Summary fixed="top">
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0}>汇总</Table.Summary.Cell>
                      <Table.Summary.Cell index={1} align="right">{total.impressions}</Table.Summary.Cell>
                      <Table.Summary.Cell index={2} align="right">{total.clicks}</Table.Summary.Cell>
                      <Table.Summary.Cell index={3} align="right">-</Table.Summary.Cell>
                      <Table.Summary.Cell index={4} align="right">{formatPercentage(total.ctr)}</Table.Summary.Cell>
                      <Table.Summary.Cell index={5} align="right">{formatCurrency(total.cpc)}</Table.Summary.Cell>
                      <Table.Summary.Cell index={6} align="right">{formatCurrency(total.spend)}</Table.Summary.Cell>
                      <Table.Summary.Cell index={7} align="right">-</Table.Summary.Cell>
                      <Table.Summary.Cell index={8} align="right">{formatCurrency(total.sales)}</Table.Summary.Cell>
                      <Table.Summary.Cell index={9} align="right">-</Table.Summary.Cell>
                      <Table.Summary.Cell index={10} align="right">{formatCurrency(total.sku_sales)}</Table.Summary.Cell>
                      <Table.Summary.Cell index={11} align="right">{formatPercentage(total.acos)}</Table.Summary.Cell>
                      <Table.Summary.Cell index={12} align="right">{total.orders}</Table.Summary.Cell>
                      <Table.Summary.Cell index={13} align="right">{total.sku_orders}</Table.Summary.Cell>
                      <Table.Summary.Cell index={14} align="right">{formatCurrency(total.cpa)}</Table.Summary.Cell>
                      <Table.Summary.Cell index={15} align="right">{formatPercentage(total.cvr)}</Table.Summary.Cell>
                      <Table.Summary.Cell index={16} align="right">{total.units}</Table.Summary.Cell>
                    </Table.Summary.Row>
                  </Table.Summary>
                );
              }}
            />
          </div>
        </Spin>
      </Space>
    </Spin>
  );
};

export default AdPlacementTab;
