import DayStrategyContent from '../../AdStrategy/components/dayStragegy';
import withModalWrapper, { ModalWrapperProps, ContentComponentProps } from '../ModalWrapper';
import { DayStrategyModalData } from '@/models/globalModals';

// 组合props类型
type DayStrategyModalProps = Omit<DayStrategyModalData, keyof ContentComponentProps> & 
  ModalWrapperProps & 
  ContentComponentProps;

// 使用高阶组件包装内容组件
const DayStrategyModal = withModalWrapper(DayStrategyContent);

export default DayStrategyModal;
export type { DayStrategyModalProps }; 