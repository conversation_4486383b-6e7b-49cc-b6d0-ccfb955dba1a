import MonthAnalysisContent from '../../AdStrategy/components/monthAnalysis';
import withModalWrapper, { ModalWrapperProps, ContentComponentProps } from '../ModalWrapper';
import { WeekMonthReportModalData } from '@/models/globalModals';

// 组合props类型
type MonthReportModalProps = Omit<WeekMonthReportModalData, keyof ContentComponentProps> & 
  ModalWrapperProps & 
  ContentComponentProps & {
    type: 'month';
    target_job_id?: string;
  };

// 使用高阶组件包装内容组件
const MonthReportModal = withModalWrapper(MonthAnalysisContent);

export default MonthReportModal;
export type { MonthReportModalProps }; 