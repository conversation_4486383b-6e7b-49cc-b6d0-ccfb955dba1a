// Ajax data interface definitions for AdStrategy components

declare namespace AjaxData {
  // Common base interface for all ajax data
  interface BaseAjaxData {
    current_time: string;
    es_id: string;
    report_status: boolean;
    role: string;
    can_edit: boolean;
    profile_id: string;
    job_id: string;
    asins: string[];
    success: boolean;
    is_generating: boolean;
    target_week: string;
    start_date: string;
    end_date: string;
  }

  // Month Analysis Ajax Data
  interface MonthAnalysisData extends BaseAjaxData {
    result: {
      market_report_week?: Strategy.WeekAnalysisData;
      market_report_month?: Strategy.WeekAnalysisData;
      market_trends?: Strategy.MarketTrends;
    };
    google_searchs?: {
      rendered_content: string;
      web_search_queries: string[];
      grounding_chunks: Array<{
        title: string;
        url: string;
      }>;
    };
  }

  // Week Strategy Ajax Data
  interface WeekStrategyData extends BaseAjaxData {
    result: {
      ads_strategy_week?: Strategy.WeekStrategyData;
      real_ads_result?: Strategy.Real_ads_result;
    };
  }

  // Day Strategy Ajax Data
  interface DayStrategyData extends BaseAjaxData {
    result: {
      ads_strategy_day: Strategy.DayStrategyData;
      real_ads_result: Strategy.Real_ads_result;
    };
    daypart_ads_adjustments: Strategy.DaypartAdsAdjustments;
  }
}