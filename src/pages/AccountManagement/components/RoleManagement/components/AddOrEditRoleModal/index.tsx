import { Form, Input, Modal } from 'antd';
import React, { useEffect } from 'react';

interface AddOrEditRoleModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  loading: boolean;
  type: 'add' | 'edit';
  initialData?: { name: string };
}

const AddOrEditRoleModal: React.FC<AddOrEditRoleModalProps> = ({
  open,
  onCancel,
  onOk,
  loading,
  type,
  initialData,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (open && type === 'edit') {
      form.setFieldsValue(initialData);
    } else {
      form.resetFields();
    }
  }, [open, type, initialData, form]);

  const handleOk = () => {
    form.validateFields().then((values) => {
      onOk(values);
    });
  };

  return (
    <Modal
      open={open}
      title={type === 'add' ? '新增角色' : '修改角色名称'}
      onCancel={onCancel}
      onOk={handleOk}
      confirmLoading={loading}
      destroyOnClose
    >
      <Form form={form} layout="vertical" style={{ marginTop: 24 }}>
        <Form.Item
          label="角色名称"
          name="name"
          rules={[
            { required: true, message: '请输入角色名称' },
            { max: 10, message: '角色名称不超过10个字符' },
          ]}
        >
          <Input placeholder="请输入角色名称，不超过10个字符" autoComplete="off" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddOrEditRoleModal;
