import React, { useState, useEffect, useRef } from 'react';
import { Alert, Button, Space } from 'antd';
import { useRequest, useSearchParams, useModel } from '@umijs/max';
import { getMessages } from '@/services/ibidder_api/user';
import { markRead } from '@/services/ibidder_api/operation'; // 引入 markRead 服务
import dayjs from 'dayjs';
import { getDayOfWeek } from '@/utils/bus';

// 导入新的高阶弹框组件
import {
  DayStrategyModal,
  WeekStrategyModal,
  MonthReportModal
} from '../Modals';
const job_idMap = {
  'market_report_month': '下月市场分析报告',
  'ads_strategy_week': '下周广告投放策略',
  'ads_strategy_day': '明天广告投放策略',
}

const ConfirmAdStrategyAlert: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { triggerRefresh } = useModel('strategyRefresh');
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { updateUnreadCount } = useModel('unreadCount');
  // 弹框状态管理 - 分别管理不同类型的弹框
  const [monthReportModal, setMonthReportModal] = useState<{
    open: boolean;
    data: any;
    messageId: number | null;
  }>({
    open: false,
    data: null,
    messageId: null
  });

  const [weekStrategyModal, setWeekStrategyModal] = useState<{
    open: boolean;
    data: any;
    messageId: number | null;
  }>({
    open: false,
    data: null,
    messageId: null
  });

  const [dayStrategyModal, setDayStrategyModal] = useState<{
    open: boolean;
    data: any;
    messageId: number | null;
  }>({
    open: false,
    data: null,
    messageId: null
  });
  const { updateAlert } = useModel('updateAlert');
  const prevEsIdRef = useRef<string>('');
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const countRef = useRef<number>(0);
  const { data: response, refresh } = useRequest(() =>
    getMessages({
      is_read: 0,
      message_type: 5,
      page_no: 1,
      page_size: 1, // 先获取10条
      parent_asin: asin,
      profile_id: profile_id,
    }),
    {
      onSuccess: (res) => {
        updateUnreadCount(res.total);
      },
    }
  );

  // 监测第一条数据的 es_id 变化
  useEffect(() => {
    const messageListData = response?.list;
    if (messageListData && messageListData.length > 0) {
      const firstMessage = messageListData[0];
      const currentEsId = firstMessage.extra_data?.es_id || '';

      // 如果有数据且 es_id 发生变化（不是初始状态）
      if (prevEsIdRef.current !== '' && prevEsIdRef.current !== currentEsId) {
        triggerRefresh();
      }

      // 更新上一次的 es_id
      prevEsIdRef.current = currentEsId;
    }
  }, [response, triggerRefresh]);

  useEffect(() => {
    if (updateAlert) {
      // 如果updateAlert变成true，取消之前的定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }

      // 重置计数器
      countRef.current = 0;
      // 开始新的定时器，执行10次
      intervalRef.current = setInterval(() => {
        countRef.current++;
        refresh();

        // 如果已经执行了10次，清除定时器
        if (countRef.current >= 10) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
        }
      }, 30_000);
    }
  }, [refresh, updateAlert]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);

  // 关闭弹框的通用函数
  const closeAllModals = () => {
    setMonthReportModal({ open: false, data: null, messageId: null });
    setWeekStrategyModal({ open: false, data: null, messageId: null });
    setDayStrategyModal({ open: false, data: null, messageId: null });
    refresh(); // 关闭弹窗后刷新列表
  };

  // 标记已读并关闭弹框
  const markItemReadAndClose = async (messageId: number | null) => {
    if (messageId === null) return;
    await markRead({ message_ids: [messageId] });
    closeAllModals();
    setTimeout(() => {
      triggerRefresh();
    }, 1000);
  };

  const messageListData = response?.list;
  // 如果没有需要确认的消息，则不显示任何内容
  if (!messageListData || messageListData.length === 0) {
    return null;
  }

  // 只显示第一条消息
  const firstMessage = messageListData[0];
  const hasReportData = !!firstMessage.extra_data?.report_data;

  if (!hasReportData) {
    return null;
  }

  const getShowDate = (item: any) => {
    if (item.extra_data.job_id === 'ads_strategy_week') {
      const formattedStartDate = item.extra_data.report_data.ads_strategy_week.start_date
      const formattedEndDate = item.extra_data.report_data.ads_strategy_week.end_date
      return `（${formattedStartDate} ~ ${formattedEndDate}）`;
    }
    if (item.extra_data.job_id === 'market_report_month') {
      const formattedStartDate = item.extra_data.report_data.market_report_month.start_date
      const formattedEndDate = item.extra_data.report_data.market_report_month.end_date
      return `（${formattedStartDate} ~ ${formattedEndDate}）`;
    }
    if (item.extra_data.job_id === 'ads_strategy_day') {
      return `（${item.extra_data.report_data.ads_strategy_day?.date || ''}${getDayOfWeek(item.extra_data.report_data.ads_strategy_day?.date || '')}）`;
    }
  }

  const showModal = (item: any) => {
    const reportData = item.extra_data;
    const job_id = reportData.job_id;

    // 根据报告类型打开对应的弹框
    if (job_id === 'market_report_month') {
      setMonthReportModal({
        open: true,
        data: item.extra_data,
        messageId: item.id
      });
    } else if (job_id === 'ads_strategy_week') {
      setWeekStrategyModal({
        open: true,
        data: item.extra_data,
        messageId: item.id
      });
    } else if (job_id === 'ads_strategy_day') {
      setDayStrategyModal({
        open: true,
        data: item.extra_data,
        messageId: item.id
      });
    }
  };

  if (firstMessage.extra_data.can_edit === false) {
    return null
  }

  return (
    <>
      <Alert
        style={{ marginBottom: 16 }}
        message={
          <Space style={{ flex: 1, justifyContent: 'space-between', width: '100%', paddingRight: '1em' }}>
            <span>
              请您审核
              <span
                style={{ color: '#4C6FFF', cursor: 'pointer' }}
                onClick={() => showModal(firstMessage)} // 注意这里传入的是 firstMessage
              >
                {job_idMap[firstMessage.extra_data.job_id as keyof typeof job_idMap]}
              </span>
              {getShowDate(firstMessage)}
            </span>
            <span>
              {dayjs(firstMessage.created_at).format('YYYY-MM-DD HH:mm')}
            </span>
          </Space>
        }
        type="info"
        showIcon
        action={
          <Button
            size="small"
            type="primary"
            onClick={() => showModal(firstMessage)}
          >
            去审核
          </Button>
        }
      />

      {/* 使用新的高阶弹框组件 */}
      {/* 月报告弹框 */}
      <MonthReportModal
        open={monthReportModal.open}
        onCancel={closeAllModals}
        job_id={monthReportModal.data?.job_id || ''}
        current_time={monthReportModal.data?.current_time || ''}
        type="month"
        onSuccess={() => markItemReadAndClose(monthReportModal.messageId)}
      />

      {/* 周策略弹框 */}
      <WeekStrategyModal
        open={weekStrategyModal.open}
        onCancel={closeAllModals}
        job_id={weekStrategyModal.data?.job_id || ''}
        current_time={weekStrategyModal.data?.current_time || ''}
        date={weekStrategyModal.data?.report_data?.ads_strategy_week?.start_date}
        isCompleteStrategy={true}
        onSuccess={() => markItemReadAndClose(weekStrategyModal.messageId)}
      />

      {/* 日策略弹框 */}
      <DayStrategyModal
        open={dayStrategyModal.open}
        onCancel={closeAllModals}
        job_id={dayStrategyModal.data?.job_id || ''}
        current_time={dayStrategyModal.data?.current_time || ''}
        date={dayStrategyModal.data?.report_data?.ads_strategy_day?.date}
        onSuccess={() => markItemReadAndClose(dayStrategyModal.messageId)}
      />
    </>
  );
};

export default ConfirmAdStrategyAlert;