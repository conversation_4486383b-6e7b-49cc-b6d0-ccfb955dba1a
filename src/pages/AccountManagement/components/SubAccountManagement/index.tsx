import { Button, Col, Dropdown, Form, Input, Modal, Row, Select, Space, Table, Tag, message } from 'antd';
import { ExclamationCircleFilled, MoreOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import {
  getAccountFilterOptions,
  getSubAccounts,
  SubAccountData,
  SubAccountItem,
  updateSubAccountStatus,
  adminUpdatePassword,
  TeamInfoData,
} from '@/services/ibidder_api/user';
import dayjs from 'dayjs';
import AddOrEditSubAccountModal from './components/AddModal';
import ChangePasswordModal from './components/ChangePasswordModal';
import { useModel } from '@umijs/max';

interface SubAccountManagementProps {
  isActive: boolean;
  teamInfo?: TeamInfoData;
  onSubAccountAdd?: () => void;
}

const SubAccountManagement: React.FC<SubAccountManagementProps> = ({
  isActive,
  teamInfo,
  onSubAccountAdd,
}) => {
  const { initialState } = useModel('@@initialState');
  const userInfo = initialState?.currentUser || {};
  const [subAccountData, setSubAccountData] = useState<SubAccountData>();
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });
  const [filterOptions, setFilterOptions] = useState<{
    role_map: { role_id: number; role_name: string; role_type: number }[];
  }>({ role_map: [] });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'add' | 'edit'>('add');
  const [currentUser, setCurrentUser] = useState<SubAccountItem | undefined>();
  const [form] = Form.useForm();
  const [isPasswordModalOpen, setIsPasswordModalOpen] = useState(false);
  const [passwordChangeLoading, setPasswordChangeLoading] = useState(false);
  

  const remainingAccounts = teamInfo
    ? (teamInfo.total_sub_accounts || 0) - (teamInfo.used_sub_accounts || 0)
    : 0;

  const fetchSubAccounts = (params: any) => {
    setLoading(true);
    const queryParams = { ...filters, ...params };
    Object.keys(queryParams).forEach((key) => {
      if (
        queryParams[key] === undefined ||
        queryParams[key] === null ||
        queryParams[key] === ''
      ) {
        delete queryParams[key];
      }
    });

    getSubAccounts(queryParams).then((res) => {
      if (res.code === 200) {
        setSubAccountData(res.data as any);
      }
    }).finally(() => {
      setLoading(false);
    }).catch((err) => {
      console.error('获取子账号列表失败:', err);
    });
  };

  const fetchFilterOptions = () => {
    getAccountFilterOptions().then((res) => {
      if (res.code === 200) {
        setFilterOptions(res.data as any);
      }
    });
  };

  useEffect(() => {
    if (isActive) {
      fetchSubAccounts({
        page: pagination.current,
        page_size: pagination.pageSize,
      });
      fetchFilterOptions();
    }
  }, [isActive]);

  const handleSearch = () => {
    const allValues = form.getFieldsValue();
    setFilters(allValues);
    const newPagination = { ...pagination, current: 1 };
    setPagination(newPagination);
    fetchSubAccounts({
      ...allValues,
      page: newPagination.current,
      page_size: newPagination.pageSize,
    });
  };

  const handleStatusChangeClick = async (record: SubAccountItem) => {
    const newStatus = !record.status;
    if (!newStatus) {
      Modal.confirm({
        title: '禁用用户',
        icon: <ExclamationCircleFilled />,
        content: '禁用用户后，此用户将无法登录账号，你可在列表中重新启用',
        okText: '禁用',
        cancelText: '取消',
        width: 400,
        async onOk() {
          try {
            const result: any = await updateSubAccountStatus({
              user_id: record.id as number,
              status: newStatus,
            });
            if (result?.code === 200) {
              message.success('禁用成功');
              fetchSubAccounts({
                page: pagination.current,
                page_size: pagination.pageSize,
              });
            } else {
              message.error(result.message || '禁用失败');
            }
          } catch (error) {
            console.error('禁用操作出错:', error);
          }
        }
      })
    } else {
      try {
        const result: any = await updateSubAccountStatus({
          user_id: record.id as number,
          status: newStatus,
        });
        if (result?.code === 200) {
          message.success('启用成功');
          fetchSubAccounts({
            page: pagination.current,
            page_size: pagination.pageSize,
          });
        } else {
          message.error(result.message || '启用失败');
        }
      } catch (error) {
        console.error('启用操作出错:', error);
      }
    }
  };

  const handleEditClick = (record: SubAccountItem) => {
    setCurrentUser(record);
    setModalType('edit');
    setIsModalOpen(true);
  };

  const handlePasswordChangeClick = (record: SubAccountItem) => {
    setCurrentUser(record);
    setIsPasswordModalOpen(true);
  };

  const handlePasswordChange = (values: any) => {
    setPasswordChangeLoading(true);
    adminUpdatePassword({
      user_id: currentUser?.id as number,
      password: values.password,
    })
      .then((res) => {
        if (res.code === 200) {
          message.success('密码修改成功');
          setIsPasswordModalOpen(false);
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        setPasswordChangeLoading(false);
      });
  };

  const columns = [
    {
      title: '用户昵称',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 170,
    },
    {
      title: '角色',
      dataIndex: 'role_name',
      key: 'role_name',
      width: 150,
      render: (text: string, record: SubAccountItem) => {
        return <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <span>{text}</span>
          {record.is_main && <Tag color="blue">主账号</Tag>}
        </div>
      },
    },
    {
      title: '登录邮箱&手机号',
      key: 'account',
      width: 190,
      render: (_: any, record: SubAccountItem) => (
        <>
          <div>{record.email}</div>
          <div style={{ color: '#999' }}>{record.phone}</div>
        </>
      ),
    },
    
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: boolean) => (
        <Tag color={status ? 'success' : 'default'}>
          {status ? '正常' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '数据权限',
      dataIndex: 'shop_permission_count',
      key: 'shop_permission_count',
      width: 130,
      render: (text: number, record: SubAccountItem) => (
        <div>
          <div>店铺权限：{record.shop_permission_count}</div>
          {/* <div style={{ color: '#999' }}>Listing权限：{record.asin_permission_count}</div> */}
        </div>
      ),
    },
    {
      title: '登录次数',
      dataIndex: 'login_count',
      key: 'login_count',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '最近登录时间',
      dataIndex: 'last_login_time',
      key: 'last_login_time',
      width: 180,
      render: (text: string) => {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 100,
      render: (_: any, record: SubAccountItem) => {
        if (userInfo?.user_type !== 1 && record.is_main) {
          return '-';
        }
        return (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Button
            type="link"
            style={{ color: '#ED1000' }}
            onClick={() => handleEditClick(record)}
          >
            编辑
          </Button>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'statusChange',
                  label: record.status ? '禁用' : '启用',
                  onClick: () => handleStatusChangeClick(record),
                },
                {
                  key: 'changePassword',
                  label: '修改密码',
                  onClick: () => handlePasswordChangeClick(record),
                },
              ],
            }}
          >
            <Button type="text" size="small" onClick={(e) => e.preventDefault()}>
              <MoreOutlined />
            </Button>
          </Dropdown>
        </div>
      )},
    },
  ];

  return (
    <div>
      <Row justify="space-between" style={{ marginBottom: '16px' }}>
        <Col>
          <span style={{ marginLeft: '12px' }}>
            当前共
            <span style={{ color: '#ED1000' }}>{teamInfo?.used_sub_accounts || 0}</span>
            位成员，还可添加
            <span style={{ color: '#ED1000' }}>{remainingAccounts}</span>位成员
          </span>
        </Col>
        <Col>
          <Form form={form} layout="inline">
            <Form.Item name="role_id">
              <Select
                placeholder="用户角色"
                style={{ width: 120 }}
                allowClear
                onChange={handleSearch}
              >
                {filterOptions.role_map.map((role) => (
                  <Select.Option key={role.role_id} value={role.role_id}>
                    {role.role_name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="status">
              <Select
                placeholder="用户状态"
                style={{ width: 120 }}
                allowClear
                onChange={handleSearch}
              >
                <Select.Option value={true}>正常</Select.Option>
                <Select.Option value={false}>禁用</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="user_name">
              <Input.Search
                placeholder="用户昵称"
                onSearch={handleSearch}
                onChange={(e) => {
                  if (e.target.value === '') {
                    handleSearch();
                  }
                }}
                onBlur={handleSearch}
                style={{ width: 200 }}
                allowClear
              />
            </Form.Item>
            <Button
            type="primary"
            onClick={() => {
              setCurrentUser(undefined);
              setModalType('add');
              setIsModalOpen(true);
            }}
            disabled={remainingAccounts <= 0}
          >
            + 添加子账号
          </Button>
          </Form>
        </Col>
      </Row>
      <Table
        loading={loading}
        columns={columns as any}
        dataSource={subAccountData?.items}
        rowKey="id"
        tableLayout="fixed"
        sticky
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: subAccountData?.total,
          showTotal: (total) => `共 ${total} 条`,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: (page, pageSize) => {
            const newPagination = { ...pagination, current: page, pageSize };
            setPagination(newPagination);
            fetchSubAccounts({
              page: newPagination.current,
              page_size: newPagination.pageSize,
            });
          },
        }}
      />
      <AddOrEditSubAccountModal
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onOk={() => {
          setIsModalOpen(false);
          fetchSubAccounts({
            page: pagination.current,
            page_size: pagination.pageSize,
          });
          if (onSubAccountAdd && modalType === 'add') {
            onSubAccountAdd();
          }
        }}
        type={modalType}
        userInfo={currentUser}
      />
      <ChangePasswordModal
        open={isPasswordModalOpen}
        onCancel={() => setIsPasswordModalOpen(false)}
        onOk={handlePasswordChange}
        loading={passwordChangeLoading}
      />
    </div>
  );
};

export default SubAccountManagement;
