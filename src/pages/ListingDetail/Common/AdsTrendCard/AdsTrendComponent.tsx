import React, { useEffect, useMemo, useState } from 'react';
import { Card, Flex, Row, Col, Typography, Spin, Tooltip, Divider, Button, Modal, Checkbox, Form, Segmented } from 'antd';
import dayjs, { type Dayjs } from 'dayjs';
import { useModel } from '@umijs/max';
import { getAdsTrend } from '@/services/ibidder_api/operation';
import AdsTrendChart from '../../AdStrategy/components/AdsTrendChartTab1';
import TrendRangePicker from './components/TrendRangePicker';
import styles from './style.less';
import { QuestionCircleOutlined, SettingOutlined, PlusOutlined } from '@ant-design/icons';
import { getBudgetColor, countryTimezoneMap } from '@/utils/bus';
import { TOOLTIP_DESCRIPTIONS } from '../../AdStrategy/components/CampaignDetailModal/utils/tooltipConfig';

const { Text } = Typography;

type VisibleState = {
  spend: boolean;
  sales: boolean;
  acos: boolean;
  cvr: boolean;
  orders: boolean;
  clicks: boolean;
  impressions: boolean;
  ctr: boolean;
  cpc: boolean;
  sku_sales: boolean;
  cpa: boolean;
};

type MetricKey = keyof VisibleState;

// 基础指标数据结构（不包含颜色）
type BaseMetric = {
  id: MetricKey;
  title: string;
  desc: string;
};



type AdsTrendCardProps = {
  style?: React.CSSProperties;
  type: 'day' | 'week' | 'month';
};

// 本地存储键名
const CUSTOM_METRICS_STORAGE_KEY = 'ads_trend_custom_metrics';

// 默认选中的指标
const DEFAULT_SELECTED_METRICS: MetricKey[] = ['spend', 'sales', 'acos', 'cvr', 'orders', 'clicks'];

// 图表颜色顺序（与 chartColors 保持一致）
const CHART_COLORS = ['#5BC4FF', '#FFD66B', '#AE8FF7', '#1DB88C', '#FF8F6B', '#5B93FF'];

const toNumber = (v: any): number => {
  if (v === null || v === undefined || v === '') return 0;
  const s = String(v).replace('%', '');
  const n = Number(s);
  return Number.isFinite(n) ? n : 0;
};

const formatCurrency = (currency: string, value: number | string) => `${currency}${Number(value || 0).toFixed(2)}`;

// 基础指标数据（不包含颜色和动态数据）- 按图片顺序排列
const BASE_METRICS: BaseMetric[] = [
  // 第一行：曝光量、点击量、点击率、ACOS
  {
    id: 'impressions',
    title: '曝光量',
    desc: TOOLTIP_DESCRIPTIONS.impressions,
  },
  {
    id: 'clicks',
    title: '点击量',
    desc: TOOLTIP_DESCRIPTIONS.clicks,
  },
  {
    id: 'acos',
    title: 'ACOS',
    desc: `ACoS是在制定时间范围内，某种类型的广告活动因广告被点击而产生的支出在由广告产生的销售额中所占的百分比。

计算规则：ACoS = 广告花费 / 广告销售额 * 100%；

商品推广广告：ACoS包括 7 天内售出的推广商品及库存中其他商品的销售额。
品牌推广： ACoS包括14 天内在亚马逊及第三方卖家处售出的推广商品及同品牌其他商品的销售额。
展示型推广销售额： ACoS 包括14 天内售出的推广商品及库存中其他商品的销售额。

您的销售数据最长可能需要 12 小时才会更新。 因此，"今天"日期范围内的销售数据可能会延迟。 我们建议您等待所有销售数据填充完毕后再评估广告活动绩效。
未成功支付的款项和 72 小时内取消的订单的金额将从销售总额中删除。`,
  },
  {
    id: 'cvr',
    title: 'CVR',
    desc: TOOLTIP_DESCRIPTIONS.cvr,
  },
  {
    id: 'ctr',
    title: 'CTR',
    desc: TOOLTIP_DESCRIPTIONS.ctr,
  },
  {
    id: 'cpc',
    title: 'CPC',
    desc: TOOLTIP_DESCRIPTIONS.cpc,
  },
  {
    id: 'orders',
    title: '广告订单',
    desc: TOOLTIP_DESCRIPTIONS.orders,
  },
  // 第三行：广告花费、广告销售额、直接成交销售额、CPA
  {
    id: 'spend',
    title: '广告花费',
    desc: `广告活动的点击或展示总费用。

特别说明：
1、一旦识别出无效点击，亚马逊最多会在 3 天内从您的支出统计数据中删除这些点击记录。日期范围（含过去 3 天内的支出）可能因点击和支出失效而有所调整；
2、因近30天（尤其近3天）亚马逊接口返回的数据，可能与亚马逊控制台展现的数据存在略微不一致，因此可能导致系统统计结果与亚马逊控制台展现的数据存在略微不一致。`,
  },
  {
    id: 'sales',
    title: '广告销售额',
    desc: `广告销售额是在某种广告活动投放期间的指定时间范围内，因广告被点击或浏览而向顾客售出的商品的价值总额。

商品推广销售额： 7 天内售出的推广商品及库存中其他商品的销售额。
品牌推广销售额： 14 天内在亚马逊及第三方卖家处售出的推广商品及同品牌其他商品的销售额。
展示型推广销售额： 14 天内售出的推广商品及库存中其他商品的销售额。

您的销售数据最长可能需要 12 小时才会更新。 因此，"今天"日期范围内的销售数据可能会延迟。 我们建议您等待所有销售数据填充完毕后再评估广告活动绩效。
未成功支付的款项和 72 小时内取消的订单的金额将从销售总额中删除。`,
  },
  {
    id: 'sku_sales',
    title: '直接成交销售额',
    desc: TOOLTIP_DESCRIPTIONS.sku_sales,
  },
  {
    id: 'cpa',
    title: 'CPA',
    desc: TOOLTIP_DESCRIPTIONS.cpa,
  },
];

// 本地存储工具函数
const getStoredMetrics = (): MetricKey[] => {
  try {
    const stored = localStorage.getItem(CUSTOM_METRICS_STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      if (Array.isArray(parsed)) {
        // 获取所有有效的指标ID
        const validMetricIds = BASE_METRICS.map(metric => metric.id);
        // 过滤掉无效的指标，只保留当前支持的指标
        const filteredMetrics = parsed.filter((metricId: string) =>
          validMetricIds.includes(metricId as MetricKey)
        );
        // 如果过滤后的指标数量在有效范围内，返回过滤后的结果
        if (filteredMetrics.length >= 1 && filteredMetrics.length <= 6) {
          return filteredMetrics;
        }
        // 如果过滤后指标数量不在有效范围，但有有效指标，取前6个或全部
        if (filteredMetrics.length > 0) {
          return filteredMetrics.slice(0, 6);
        }
      }
    }
  } catch (error) {
    console.warn('Failed to parse stored metrics:', error);
  }
  return DEFAULT_SELECTED_METRICS;
};

const setStoredMetrics = (metrics: MetricKey[]) => {
  try {
    localStorage.setItem(CUSTOM_METRICS_STORAGE_KEY, JSON.stringify(metrics));
  } catch (error) {
    console.warn('Failed to store metrics:', error);
  }
};

const computePresetRange = (type: 'day' | 'week' | 'month', count: number, country?: string): [Dayjs, Dayjs] => {
  const tz = country ? countryTimezoneMap[country.toLowerCase() as keyof typeof countryTimezoneMap] : undefined;
  const now = tz ? dayjs().tz(tz) : dayjs();

  if (type === 'day') {
    const end = now.endOf('day');
    const start = end.subtract(count - 1, 'day').startOf('day');
    return [start, end];
  }
  if (type === 'week') {
    // Use Monday-Sunday as display, but for request we only need date range
    const end = now.endOf('day');
    const start = end.subtract(count * 7 - 1, 'day').startOf('day');
    return [start, end];
  }
  // month
  const end = now.endOf('day');
  const start = now.startOf('month').subtract(count - 1, 'month');
  return [start, end];
};

const AdsTrendCard: React.FC<AdsTrendCardProps> = React.memo((props) => {
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  const asins = productInfo?.asins || [];
  const parent_asin = productInfo?.parent_asin || '';
  const profile_id = productInfo?.profile_id as number | undefined;
  const country = productInfo?.country || '';

  const { type } = props;
  const [loading, setLoading] = useState(false);
  const [range, setRange] = useState<[Dayjs, Dayjs]>(() => {
    if (type === 'day') return computePresetRange('day', 14, country);
    if (type === 'week') return computePresetRange('week', 14, country);
    return computePresetRange('month', 6, country);
  });
  const [rangeForChart, setRangeForChart] = useState(range);

  const [data, setData] = useState<API.AdsTrendResponse | null>(null);
  const [visible, setVisible] = useState<VisibleState>({
    spend: true,
    sales: true,
    acos: true,
    cvr: true,
    orders: true,
    clicks: true,
    impressions: false,
    ctr: false,
    cpc: false,
    sku_sales: false,
    cpa: false,
  });

  // 自定义指标相关状态
  const [selectedMetrics, setSelectedMetrics] = useState<MetricKey[]>(() => getStoredMetrics());
  const [isCustomModalVisible, setIsCustomModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 自定义指标处理函数
  const handleCustomMetricsClick = () => {
    // 设置表单初始值
    form.setFieldsValue({
      selectedMetrics: selectedMetrics
    });
    setIsCustomModalVisible(true);
  };

  const handleCustomMetricsConfirm = () => {
    form.validateFields().then((values) => {
      const newSelectedMetrics = values.selectedMetrics || [];

      if (newSelectedMetrics.length < 1 || newSelectedMetrics.length > 6) {
        return;
      }

      setSelectedMetrics(newSelectedMetrics);
      setStoredMetrics(newSelectedMetrics);

      // 更新 visible 状态
      const newVisible: VisibleState = {
        spend: newSelectedMetrics.includes('spend'),
        sales: newSelectedMetrics.includes('sales'),
        acos: newSelectedMetrics.includes('acos'),
        cvr: newSelectedMetrics.includes('cvr'),
        orders: newSelectedMetrics.includes('orders'),
        clicks: newSelectedMetrics.includes('clicks'),
        impressions: newSelectedMetrics.includes('impressions'),
        ctr: newSelectedMetrics.includes('ctr'),
        cpc: newSelectedMetrics.includes('cpc'),
        sku_sales: newSelectedMetrics.includes('sku_sales'),
        cpa: newSelectedMetrics.includes('cpa'),
      };
      setVisible(newVisible);
      setIsCustomModalVisible(false);
    }).catch((error) => {
      console.error('Form validation failed:', error);
    });
  };

  const handleCustomMetricsCancel = () => {
    form.resetFields();
    setIsCustomModalVisible(false);
  };



  const fetchData = async (start: Dayjs, end: Dayjs) => {
    if (!profile_id || !parent_asin) return;
    setLoading(true);
    try {
      // For week and month types, adjust start/end times to period boundaries
      let apiStartTime: string;
      let apiEndTime: string;

      if (type === 'week') {
        // For week: start should be Monday of the start week, end should be Sunday of the end week
        const startWeekBegin = start.startOf('week'); // Monday
        const endWeekEnd = end.endOf('week'); // Sunday
        apiStartTime = startWeekBegin.format('YYYY-MM-DD');
        apiEndTime = endWeekEnd.format('YYYY-MM-DD');
      } else if (type === 'month') {
        // For month: start should be 1st day of start month, end should be last day of end month
        const startMonthBegin = start.startOf('month');
        const endMonthEnd = end.endOf('month');
        apiStartTime = startMonthBegin.format('YYYY-MM-DD');
        apiEndTime = endMonthEnd.format('YYYY-MM-DD');
      } else {
        // For day type, use original logic
        apiStartTime = start.format('YYYY-MM-DD');
        apiEndTime = end.format('YYYY-MM-DD');
      }

      const res = await getAdsTrend({
        asins,
        parent_asin,
        profile_id: Number(profile_id),
        start_time: apiStartTime,
        end_time: apiEndTime,
        type,
      });
      // @ts-ignore
      if (res && (res.code === 200 || res.list || res.sum)) {
        // Some request util may return data directly
        const payload: API.AdsTrendResponse = (res.data || res) as any;
        setData(payload);
        setRangeForChart([start, end]);
      } else {
        setData(null);
        setRangeForChart([start, end]);
      }
    } catch (e) {
      setData(null);
      setRangeForChart([start, end]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (range) fetchData(range[0], range[1]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type, range?.[0]?.valueOf(), range?.[1]?.valueOf(), profile_id, parent_asin]);

  // 初始化 visible 状态基于选中的指标
  useEffect(() => {
    const newVisible: VisibleState = {
      spend: selectedMetrics.includes('spend'),
      sales: selectedMetrics.includes('sales'),
      acos: selectedMetrics.includes('acos'),
      cvr: selectedMetrics.includes('cvr'),
      orders: selectedMetrics.includes('orders'),
      clicks: selectedMetrics.includes('clicks'),
      impressions: selectedMetrics.includes('impressions'),
      ctr: selectedMetrics.includes('ctr'),
      cpc: selectedMetrics.includes('cpc'),
      sku_sales: selectedMetrics.includes('sku_sales'),
      cpa: selectedMetrics.includes('cpa'),
    };
    setVisible(newVisible);
  }, [selectedMetrics]);

  const chartData = useMemo(() => {
    if (
      range?.[0]?.valueOf() !== rangeForChart?.[0]?.valueOf() ||
      range?.[1]?.valueOf() !== rangeForChart?.[1]?.valueOf()
    ) {
      return {
        xAxisData: [],
        spend: [],
        sales: [],
        acos: [],
        cvr: [],
        orders: [],
        clicks: [],
        impressions: [],
        ctr: [],
        cpc: [],
        spend_ratio: [],
        sku_sales: [],
        cpa: [],
        sales_ratio: [],
      };
    }

    const xAxisData: string[] = [];
    const spend: number[] = [];
    const sales: number[] = [];
    const acos: number[] = [];
    const cvr: number[] = [];
    const orders: number[] = [];
    const clicks: number[] = [];
    const impressions: number[] = [];
    const ctr: number[] = [];
    const cpc: number[] = [];
    const sku_sales: number[] = [];
    const cpa: number[] = [];

    if (data?.list?.length) {
      data.list.forEach((item) => {
        const startDate = item.startDate;
        if (type === 'week') {
          const startD = dayjs(startDate);
          const endD = startD.add(6, 'day');
          xAxisData.push(`${startD.format('YYYY-MM-DD')}～${endD.format('YYYY-MM-DD')}`);
        } else if (type === 'month') {
          xAxisData.push(dayjs(startDate).format('YYYY-MM'));
        } else {
          xAxisData.push(startDate);
        }
        const spendValue = Number(item.spend || 0);
        const salesValue = Number(item.sales || 0);
        const ordersValue = Number(item.orders || 0);
        const clicksValue = Number(item.clicks || 0);
        const impressionsValue = Number(item.impressions || 0);
        const cpcValue = Number(item.cpc || 0);
        const cpaValue = Number(item.cpa || 0);

        spend.push(spendValue);
        sales.push(salesValue);
        acos.push(toNumber(item.acos));
        cvr.push(toNumber(item.cvr));
        orders.push(ordersValue);
        clicks.push(clicksValue);
        impressions.push(impressionsValue);
        ctr.push(toNumber(item.ctr));
        cpc.push(cpcValue);
        // 直接成交销售额 - 使用API返回的sku_sales字段
        sku_sales.push(Number(item.sku_sales || 0));
        cpa.push(cpaValue);
      });
    }

    return {
      xAxisData,
      spend: visible.spend ? spend : [],
      sales: visible.sales ? sales : [],
      acos: visible.acos ? acos : [],
      cvr: visible.cvr ? cvr : [],
      orders: visible.orders ? orders : [],
      clicks: visible.clicks ? clicks : [],
      impressions: visible.impressions ? impressions : [],
      ctr: visible.ctr ? ctr : [],
      cpc: visible.cpc ? cpc : [],
      sku_sales: visible.sku_sales ? sku_sales : [],
      cpa: visible.cpa ? cpa : [],
    };
  }, [data, type, visible, range, rangeForChart]);

  const keyForChart = useMemo(() => {
    // Force re-mount to update options due to AdsTrendChart's internal effect deps
    return `${type}-${rangeForChart?.[0]?.valueOf()}-${rangeForChart?.[1]?.valueOf()}-${JSON.stringify(visible)}`;
  }, [type, rangeForChart, visible]);

  const sum = data?.sum;
  const prev = data?.previous_sum as any;

  // 生成指标数据的函数
  const createMetricData = (baseMetric: BaseMetric, colorIndex: number) => {
    const { id, title, desc } = baseMetric;
    const color = CHART_COLORS[colorIndex % CHART_COLORS.length];

    let value: string;
    let prevValue: string;
    let ratio: string | undefined;

    switch (id) {
      case 'spend':
        value = formatCurrency(currency, sum?.spend || 0);
        prevValue = formatCurrency(currency, prev?.spend || 0);
        ratio = prev?.spend_ratio;
        break;
      case 'sales':
        value = formatCurrency(currency, sum?.sales || 0);
        prevValue = formatCurrency(currency, prev?.sales || 0);
        ratio = prev?.sales_ratio;
        break;
      case 'acos':
        value = sum?.acos || '-';
        prevValue = prev?.acos || '-';
        ratio = prev?.acos_ratio;
        break;
      case 'cvr':
        value = sum?.cvr || '-';
        prevValue = prev?.cvr || '-';
        ratio = prev?.cvr_ratio;
        break;
      case 'orders':
        value = String(sum?.orders || 0);
        prevValue = String(prev?.orders || 0);
        ratio = prev?.orders_ratio;
        break;
      case 'clicks':
        value = String(sum?.clicks || 0);
        prevValue = String(prev?.clicks || 0);
        ratio = prev?.clicks_ratio;
        break;
      case 'impressions':
        value = String(sum?.impressions || 0);
        prevValue = String(prev?.impressions || 0);
        ratio = prev?.impressions_ratio;
        break;
      case 'ctr':
        value = sum?.ctr || '-';
        prevValue = prev?.ctr || '-';
        ratio = prev?.ctr_ratio;
        break;
      case 'cpc':
        value = formatCurrency(currency, sum?.cpc || 0);
        prevValue = formatCurrency(currency, prev?.cpc || 0);
        ratio = prev?.cpc_ratio;
        break;
      case 'sku_sales':
        value = formatCurrency(currency, sum?.sku_sales || 0);
        prevValue = formatCurrency(currency, prev?.sku_sales || 0);
        ratio = prev?.sku_sales_ratio;
        break;
      case 'cpa':
        value = formatCurrency(currency, sum?.cpa || 0);
        prevValue = formatCurrency(currency, prev?.cpa || 0);
        ratio = prev?.cpa_ratio;
        break;
      default:
        value = '-';
        prevValue = '-';
        ratio = undefined;
    }

    return {
      id,
      title,
      desc,
      color,
      value,
      prevValue,
      ratio,
      onClick: () => setVisible(v => ({ ...v, [id]: !v[id] })),
    };
  };

  // 根据选中的指标生成 metrics 数组
  const metrics = selectedMetrics.map((metricId, index) => {
    const baseMetric = BASE_METRICS.find(m => m.id === metricId);
    if (!baseMetric) return null;
    return createMetricData(baseMetric, index);
  }).filter(Boolean) as ReturnType<typeof createMetricData>[];

  // 是否显示添加指标卡片
  const showAddMetricCard = selectedMetrics.length < 6;

  const MetricCard = ({ id, title, desc, color, value, prevValue, ratio, onClick }: ReturnType<typeof createMetricData>) => (
    <Col xs={12} sm={12} md={6} lg={4} xl={4}>
      <div
        onClick={onClick}
        className={styles.metricCard}
        style={{
          background: visible[id] ? '#fff' : '#F9FAFB',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.09)',
          padding: '16px 20px',
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          gap: '16px',
          borderLeft: `4px solid ${visible[id] ? color : '#F9FAFB'}`,
        }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
          <Text style={{ color: '#595959', fontSize: '14px' }}>{title}
            <Tooltip title={desc} overlayStyle={{ whiteSpace: 'pre-wrap', width: 320 }}>
              <QuestionCircleOutlined style={{ color: '#86909C', fontSize: '14px', marginLeft: '4px' }} />
            </Tooltip>
          </Text>
          <div style={{ fontWeight: 'bold', fontSize: '24px', lineHeight: '1.2' }}>
            {value}
          </div>
          <Tooltip
            overlayStyle={{ width: 400 }}
            placement="bottomRight"
            title={
              <div style={{ whiteSpace: 'nowrap', display: 'flex', flexDirection: 'column', gap: 10, padding: 8 }}>
                <div>{title}</div>
                <div style={{ display: 'flex', gap: 20 }}>
                  <span>本期：{value}</span>
                  <span>{data?.sum?.start_time}~{data?.sum?.end_time}</span>
                </div>
                <div style={{ display: 'flex', gap: 20 }}>
                  <span>环比：{prevValue}</span>
                  <span>{data?.previous_sum?.start_time}~{data?.previous_sum?.end_time}</span>
                  <span style={{ color: getBudgetColor(0, ratio || 0) }}>
                    {ratio ? ratio.startsWith('-') ? ratio : '+' + ratio : '-'}
                  </span>
                </div>
              </div>
            }
          >
            <div style={{ color: '#8c8c8c', fontSize: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>{prevValue}</span>
              <Divider type="vertical" />
              <span style={{ color: getBudgetColor(0, ratio || 0) }}>
                {ratio ? ratio.startsWith('-') ? ratio : '+' + ratio : '-'}
              </span>
            </div>
          </Tooltip>
        </div>
      </div>
    </Col>
  );

  // 添加指标卡片组件
  const AddMetricCard = () => (
    <Col xs={12} sm={12} md={6} lg={4} xl={4}>
      <div
        onClick={handleCustomMetricsClick}
        className={styles.metricCard}
        style={{
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.09)',
          padding: '16px 20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          gap: '8px',
          width: '100%',
          height: '100%',
        }}
      >
        <PlusOutlined style={{ fontSize: '16px' }} />
        <Text>添加指标</Text>
      </div>
    </Col>
  );

  const metricCards = (
    <Row gutter={[16, 16]}>
      {metrics.map(metric => (
        <MetricCard {...metric} key={metric.id} />
      ))}
      {showAddMetricCard && <AddMetricCard />}
    </Row>
  );

  return (
    <div>
      <Spin spinning={loading}>
        {metricCards}
        <div style={{ width: '100%', height: 300 }}>
          <AdsTrendChart
            key={keyForChart}
            currency={currency}
            data={chartData}
            hideLegend
            dateType={type}
            chartOption={{
              axisLabel: type === 'week' ? { interval: 0 } : {},
            }}
          />
        </div>
      </Spin>

      {/* 自定义指标选择弹框 */}
      <Modal
        title={<>自定义指标 <Text type='secondary' style={{ fontWeight: 'semibold' }}>最少选择1项，最多选择6项</Text></>}
        open={isCustomModalVisible}
        onOk={handleCustomMetricsConfirm}
        onCancel={handleCustomMetricsCancel}
        okText="保存并应用"
        cancelText="取消"
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 24 }}
        >
          <Form.Item
            name="selectedMetrics"
            rules={[
              {
                validator: (_, value) => {
                  if (!value || value.length < 1) {
                    return Promise.reject(new Error('至少选择1个指标'));
                  }
                  if (value.length > 6) {
                    return Promise.reject(new Error('最多选择6个指标'));
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 16]}>
                {BASE_METRICS.map((metric) => (
                  <Col span={6} key={metric.id}>
                    <Checkbox
                      value={metric.id}
                      style={{ width: '100%' }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                        <span>{metric.title}</span>
                        <Tooltip title={metric.desc} overlayStyle={{ whiteSpace: 'pre-wrap', maxWidth: 400 }}>
                          <QuestionCircleOutlined style={{ color: '#86909C', fontSize: '12px' }} />
                        </Tooltip>
                      </div>
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}, (prevProps, nextProps) => {
  // 只有当 type 或 style 真正改变时才重新渲染
  return prevProps.type === nextProps.type &&
    JSON.stringify(prevProps.style) === JSON.stringify(nextProps.style);
});

export default AdsTrendCard;
