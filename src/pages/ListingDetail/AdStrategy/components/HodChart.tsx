import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

interface HodChartProps {
  hodBidding: {
    hour: number;
    adjustment: number;
  }[];
  multipleXAxis?: boolean;
}

const HodChart: React.FC<HodChartProps> = (props) => {
  const { hodBidding, multipleXAxis } = props
  const chartRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // 检查 DOM 元素和数据是否存在
    if (!chartRef.current || !hodBidding || hodBidding.length === 0) {
      return;
    }

    // 确保在已有图表实例时先销毁
    let chartInstance = echarts.getInstanceByDom(chartRef.current);
    if (chartInstance) {
      chartInstance.dispose();
    }

    // 初始化图表
    chartInstance = echarts.init(chartRef.current);

    const option = {
      tooltip: {
        trigger: 'axis',
        confine: true,
        backgroundColor: 'rgba(0,0,0,0.65)', // 黑色背景
        borderColor: '#444',
        textStyle: {
          color: '#fff', // 白色文字
        },
        formatter: function (params: any) {
          const value = params[0].value;
          return `调整比例: ${value > 0 ? '+' : ''}${(value * 100).toFixed(2)}%`;
        }
      },
      grid: {
        left: 10,
        right: 10,
        bottom: 10,
        top: 30,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: hodBidding.map(item => `${item.hour}:00`),
        boundaryGap: true,
        axisLabel: multipleXAxis ? {
          interval: 0,
          rotate: 45
        } : {}
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: (value: number) => `${value > 0 ? '+' : ''}${value * 100}%`
        }
      },
      series: [{
        data: hodBidding.map(item => item.adjustment),
        type: 'bar',
        itemStyle: {
          color: function (params: any) {
            return params.value >= 0 ? '#ff4d4f' : '#52c41a';
          }
        },
        barMaxWidth: 30,
        label: {
          show: true,
          position: 'top',
          formatter: function (params: any) {
            return `${params.value > 0 ? '+' : ''}${(params.value * 100).toFixed(2)}%`;
          }
        },
      }]
    };

    chartInstance.setOption(option);

    const handleResize = () => {
      chartInstance?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      chartInstance?.dispose();
      window.removeEventListener('resize', handleResize);
    };
  }, [hodBidding]);

  return <div ref={chartRef} style={{ width: '100%', height: '350px' }} />;
};

export default HodChart;