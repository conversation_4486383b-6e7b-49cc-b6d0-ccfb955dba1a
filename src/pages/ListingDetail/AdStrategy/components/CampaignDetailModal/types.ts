export interface GetCampaignDailyDataParams {
  campaign_id: number;
  group_by: 'day' | 'week' | 'month';
  end_date: string;
  campaign_type: string;
  profile_id: number;
  start_date: string;
}

export interface GetCampaignSequentialAnalysisParams {
  campaign_id: number;
  campaign_type: string;
  end_date: string;
  profile_id: number;
  start_date: string;
}

export interface GetCampaignSynchronousAnalysisParams {
  campaign_id: number;
  campaign_type: string;
  end_date: string;
  profile_id: number;
  start_date: string;
}

// Data Record Types

export interface DailyTableRecord {
  date: string;
  weekday: string;
  budget: number;
  impressions: number;
  clicks: number;
  orders: number;
  units: number;
  sales: number;
  spend: number;
  campaignType: string;
  campaignId: number;
  topOfSearchImpressionShare: number;
  sku_orders: number;
  sku_sales: number;
  acos: string;
  cvr: string;
  ctr: string;
  cpc: string;
  cpa: string;
  click_ratio: string;
  spend_ratio: string;
  sales_ratio: string;
}

export interface DailyDataResponse {
  list: DailyTableRecord[];
  total: {
    budget: number;
    spend: number;
    impressions: number;
    clicks: number;
    orders: number;
    units: number;
    sales: number;
    sku_orders: number;
    sku_sales: number;
    topOfSearchImpressionShare: number;
    acos: string;
    cvr: string;
    ctr: string;
    cpc: string;
    cpa: string;
  };
}

export interface DailyChartData {
  dates: string[];
  spend: number[];
  sales: number[];
  orders: number[];
  budget: number[];
  acos: number[];
  cvr: number[];
  ctr: number[];
  impressions: number[];
  clicks: number[];
  cpc: number[];
  cpa: number[];
  units: number[];
  sku_orders: number[];
  sku_sales: number[];
  topOfSearchImpressionShare: number[];
  click_ratio: number[];
  spend_ratio: number[];
  sales_ratio: number[];
}

export interface SequentialAnalysisData {
  current_period: {
    start_date: string;
    end_date: string;
    total: {
      budget: number;
      spend: number;
      impressions: number;
      clicks: number;
      orders: number;
      units: number;
      sales: number;
      sku_orders: number;
      sku_sales: number;
      topOfSearchImpressionShare: number;
      acos: string;
      cvr: string;
      ctr: string;
      cpc: string;
      cpa: string;
      click_ratio: string;
      spend_ratio: string;
      sales_ratio: string;
    };
  };
  previous_period: {
    start_date: string;
    end_date: string;
    total: {
      budget: number;
      spend: number;
      impressions: number;
      clicks: number;
      orders: number;
      units: number;
      sales: number;
      sku_orders: number;
      sku_sales: number;
      topOfSearchImpressionShare: number;
      acos: string;
      cvr: string;
      ctr: string;
      cpc: string;
      cpa: string;
      click_ratio: string;
      spend_ratio: string;
      sales_ratio: string;
    };
  };
  change: {
    budget: number;
    spend: number;
    impressions: number;
    clicks: number;
    orders: number;
    units: number;
    sales: number;
    sku_orders: number;
    sku_sales: number;
    topOfSearchImpressionShare: number;
    acos: string;
    cvr: string;
    ctr: string;
    cpc: string;
    cpa: string;
    click_ratio: string;
    spend_ratio: string;
    sales_ratio: string;
  };
  change_ratio: {
    budget: string;
    spend: string;
    impressions: string;
    clicks: string;
    orders: string;
    units: string;
    sales: string;
    sku_orders: string;
    sku_sales: string;
    topOfSearchImpressionShare: string;
    acos: string;
    cvr: string;
    ctr: string;
    cpc: string;
    cpa: string;
    click_ratio: string;
    spend_ratio: string;
    sales_ratio: string;
  };
}

export interface SynchronousAnalysisRecord {
  date: string;
  budget: number;
  spend: number;
  impressions: number;
  clicks: number;
  orders: number;
  units: number;
  sales: number;
  sku_orders: number;
  sku_sales: number;
  topOfSearchImpressionShare: number;
  acos: string;
  cvr: string;
  ctr: string;
  cpc: string;
  cpa: string;
  click_ratio: string;
  spend_ratio: string;
  sales_ratio: string;
  children?: SynchronousAnalysisChildRecord[];
}

export interface SynchronousAnalysisChildRecord {
  date: string;
  weekday: string;
  budget: number;
  impressions: number;
  clicks: number;
  orders: number;
  units: number;
  sales: number;
  spend: number;
  campaignType: string;
  campaignId: number;
  topOfSearchImpressionShare: number;
  sku_orders: number;
  sku_sales: number;
  acos: string;
  cvr: string;
  ctr: string;
  cpc: string;
  cpa: string;
}

export interface SynchronousAnalysisData {
  list: SynchronousAnalysisRecord[];
  total: {
    budget: number;
    spend: number;
    impressions: number;
    clicks: number;
    orders: number;
    units: number;
    sales: number;
    sku_orders: number;
    sku_sales: number;
    topOfSearchImpressionShare: number;
    acos: string;
    cvr: string;
    ctr: string;
    cpc: string;
    cpa: string;
    click_ratio: string;
    spend_ratio: string;
    sales_ratio: string;
  };
}


export interface CampaignPlacementResponse {
  "Detail Page on-Amazon": {
    list: DetailPageOnAmazonList[];
    total: DetailPageOnAmazonTotal;
  };
  "Other on-Amazon": {
    list: OtherOnAmazonList[];
    total: OtherOnAmazonTotal;
  };
  Summary: {
    list: SummaryList[];
    total: SummaryTotal;
  };
  "Top of Search on-Amazon": {
    list: TopOfSearchOnAmazonList[];
    total: TopOfSearchOnAmazonTotal;
  };
}

export interface DetailPageOnAmazonList {
  acos: number;
  clicks: number;
  clicks_ratio: number;
  cpa: number;
  cpc: number;
  ctr: number;
  cvr: number;
  date: string;
  impressions: number;
  orders: number;
  placement: string;
  sales: number;
  sales_ratio: number;
  spend: number;
  spend_ratio: number;
  sku_orders: number;
  sku_sales: number;
  units: number;
}

export interface DetailPageOnAmazonTotal {
  acos: number;
  clicks: number;
  clicks_ratio: number;
  cpa: number;
  cpc: number;
  ctr: number;
  cvr: number;
  impressions: number;
  orders: number;
  placement: string;
  sales: number;
  sales_ratio: number;
  spend: number;
  spend_ratio: number;
  sku_orders: number;
  sku_sales: number;
  units: number;
}

export interface OtherOnAmazonList {
  acos: number;
  clicks: number;
  clicks_ratio: number;
  cpa: number;
  cpc: number;
  ctr: number;
  cvr: number;
  date: string;
  impressions: number;
  orders: number;
  placement: string;
  sales: number;
  sales_ratio: number;
  spend: number;
  spend_ratio: number;
  sku_orders: number;
  sku_sales: number;
  units: number;
}

export interface OtherOnAmazonTotal {
  acos: number;
  clicks: number;
  clicks_ratio: number;
  cpa: number;
  cpc: number;
  ctr: number;
  cvr: number;
  impressions: number;
  orders: number;
  placement: string;
  sales: number;
  sales_ratio: number;
  spend: number;
  spend_ratio: number;
  sku_orders: number;
  sku_sales: number;
  units: number;
}

export interface SummaryList {
  acos: number;
  clicks: number;
  clicks_ratio: number;
  cpa: number;
  cpc: number;
  ctr: number;
  cvr: number;
  impressions: number;
  orders: number;
  placement: string;
  sales: number;
  sales_ratio: number;
  spend: number;
  spend_ratio: number;
  sku_orders: number;
  sku_sales: number;
  units: number;
}

export interface SummaryTotal {
  acos: number;
  clicks: number;
  clicks_ratio: number;
  cpa: number;
  cpc: number;
  ctr: number;
  cvr: number;
  impressions: number;
  orders: number;
  placement: string;
  sales: number;
  sales_ratio: number;
  spend: number;
  spend_ratio: number;
  sku_orders: number;
  sku_sales: number;
  units: number;
}

export interface TopOfSearchOnAmazonList {
  acos: number;
  clicks: number;
  clicks_ratio: number;
  cpa: number;
  cpc: number;
  ctr: number;
  cvr: number;
  date: string;
  impressions: number;
  orders: number;
  placement: string;
  sales: number;
  sales_ratio: number;
  spend: number;
  spend_ratio: number;
  sku_orders: number;
  sku_sales: number;
  units: number;
}

export interface TopOfSearchOnAmazonTotal {
  acos: number;
  clicks: number;
  clicks_ratio: number;
  cpa: number;
  cpc: number;
  ctr: number;
  cvr: number;
  impressions: number;
  orders: number;
  placement: string;
  sales: number;
  sales_ratio: number;
  spend: number;
  spend_ratio: number;
  sku_orders: number;
  sku_sales: number;
  units: number;
}