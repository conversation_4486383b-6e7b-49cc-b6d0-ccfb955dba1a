import { Card, Descriptions, Tag, List, Modal, Form, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import styles from './index.less';
import { getUserInfo, UserInfoData, updatePassword } from '@/services/ibidder_api/user';

const AccountInfo: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfoData>();

  useEffect(() => {
    getUserInfo().then((res) => {
      if (res.code === 200) {
        setUserInfo(res.data as any);
      }
    });
  }, []);

  const title = (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <span
        style={{
          backgroundColor: 'red',
          width: '4px',
          height: '16px',
          marginRight: '8px',
        }}
      />
      <span>账号信息</span>
    </div>
  );

  return (
    <Card>
      <Descriptions title={title} column={2}>
        <Descriptions.Item label="用户名">{userInfo?.username || '-'}</Descriptions.Item>
        <Descriptions.Item label="手机号">{userInfo?.phone || '-'}</Descriptions.Item>
        <Descriptions.Item label="邮箱">{userInfo?.email || '-'}</Descriptions.Item>
        <Descriptions.Item label="公司">{userInfo?.company || '-'}</Descriptions.Item>
        <Descriptions.Item label="角色名称">{userInfo?.role_name || '-'}</Descriptions.Item>
        <Descriptions.Item label="是否主账号">
          {userInfo?.is_main_account ? '是' : '否'}
        </Descriptions.Item>
        <Descriptions.Item label="账号状态">
          <Tag color={userInfo?.status ? 'success' : 'default'}>
            {userInfo?.status ? '正常' : '禁用'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="账号有效期">
          {userInfo?.valid_until || '-'}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

const MemberCenter: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  const handleUpdatePassword = async (values: any) => {
    try {
      const res = await updatePassword({
        old_password: values.old_password,
        new_password: values.new_password,
      });
      if (res.code === 200) {
        message.success('密码修改成功');
        setIsModalVisible(false);
        form.resetFields();
      } else {
        message.error(res.message || '密码修改失败');
      }
    } catch (error) {
      console.error('请求失败，请重试', error);
    }
  };

  const passwordValidator = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    if (value.length < 8 || value.length > 20) {
      return Promise.reject(new Error('密码长度必须在8-20位之间'));
    }
    let types = 0;
    if (/[a-zA-Z]/.test(value)) types++;
    if (/\d/.test(value)) types++;
    if (/[^a-zA-Z0-9]/.test(value)) types++;
    if (types < 2) {
      return Promise.reject(new Error('密码必须包含字母、符号或数字中至少两项'));
    }
    return Promise.resolve();
  };

  const securityData = [
    {
      title: '登录密码',
      description:
        '安全性高的密码可以使帐号更安全。建议您定期更换,设置一个包含字母,符号或数字中至少两项,且长度在8-20位的密码。',
      actions: [
        <a key="modify" onClick={() => setIsModalVisible(true)}>
          修改
        </a>,
      ],
    },
  ];

  const securityTitle = (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <span
        style={{
          backgroundColor: 'red',
          width: '4px',
          height: '16px',
          marginRight: '8px',
        }}
      />
      <span>安全设置</span>
    </div>
  );

  return (
    <PageContainer
      header={{
        title: null,
      }}
    >
      <div className={styles.container}>
        <AccountInfo />
        <Card style={{ marginTop: '24px' }} title={securityTitle}>
          <List
            itemLayout="horizontal"
            dataSource={securityData}
            renderItem={(item) => (
              <List.Item actions={item.actions}>
                <List.Item.Meta title={item.title} description={item.description} />
              </List.Item>
            )}
          />
        </Card>
      </div>
      <Modal
        title="修改密码"
        visible={isModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        destroyOnClose
      >
        <Form
          form={form}
          name="updatePasswordForm"
          onFinish={handleUpdatePassword}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
          autoComplete="off"
        >
          <Form.Item
            label="旧密码"
            name="old_password"
            rules={[{ required: true, message: '请输入旧密码' }]}
          >
            <Input.Password placeholder="请输入" />
          </Form.Item>
          <Form.Item
            label="新密码"
            name="new_password"
            rules={[
              { required: true, message: '请输入新密码' },
              { validator: passwordValidator },
            ]}
            // help="密码包含字母、符号或数字中至少两项，且长度在8-20位之间"
          >
            <Input.Password placeholder="请输入" />
          </Form.Item>
          <Form.Item
            label="确认新密码"
            name="confirm_new_password"
            dependencies={['new_password']}
            rules={[
              { required: true, message: '请重复密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('new_password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password placeholder="请重复密码" />
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default MemberCenter;
