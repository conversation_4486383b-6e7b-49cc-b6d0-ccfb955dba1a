export default {
  'GET /api/shops': (req: any, res: any) => {
    const { page = 1, page_size = 10 } = req.query;

    const mockData = [
      {
        market: 'NA',
        seller_id: 'A2XKTOFI7HJT3K',
        account_name: '众栎',
        shop_list: [
          {
            id: '1',
            shop_name: '众栎-US',
            profile_id: '12345',
            country_code: 'US',
            shop_status: true,
            ads_status: true,
            status: 'enabled',
            sync_status: true,
            update_time: '2025-08-11 16:12:52',
            authorized_name: 'sue',
          },
          {
            id: '2',
            shop_name: '众栎-CA',
            profile_id: '12346',
            country_code: 'CA',
            shop_status: false,
            ads_status: false,
            status: 'disabled',
            sync_status: false,
            update_time: '2025-08-11 16:12:52',
            authorized_name: '-',
          },
        ],
      },
      {
        market: 'EU',
        seller_id: 'A18IUJBYDSEUWA',
        account_name: '众栎',
        shop_list: [
          {
            id: '3',
            shop_name: '众栎-UK',
            profile_id: '23456',
            country_code: 'UK',
            shop_status: true,
            ads_status: true,
            status: 'disabled',
            sync_status: false,
            update_time: '2025-08-11 16:12:52',
            authorized_name: 'sue',
          },
        ],
      },
    ];

    const start = (page - 1) * page_size;
    const end = start + parseInt(page_size);
    const list = mockData.slice(start, end);

    res.json({
      code: 200,
      data: {
        list,
        total: mockData.length,
        has_shop: true,
      },
      message: 'success',
    });
  },

  'GET /api/shops/filter-options': (req: any, res: any) => {
    res.json({
      code: 200,
      data: {
        market: ['NA', 'EU', 'FE'],
        shop: [
          { name: 'MiuLee-US', id: '1' },
          { name: 'MiuLee-UK', id: '2' },
          { name: 'MiuLee-DE', id: '3' },
          { name: 'MiuLee-FR', id: '4' },
          { name: 'MiuLee-IT', id: '5' },
          { name: 'MiuLee-ES', id: '6' },
          { name: 'MiuLee-CA', id: '7' },
          { name: 'MiuLee-JP', id: '8' },
          { name: 'MiuLee-AU', id: '9' },
        ],
        country_code: ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'CA', 'JP', 'AU'],
        seller_id: ['A2XKTOFT7HJT3K'],
      },
      message: 'success',
    });
  },

  'POST /api/shops': (req: any, res: any) => {
    res.json({
      code: 200,
      data: { id: Date.now().toString() },
      message: '店铺添加成功',
    });
  },

  'PUT /api/shops/:id': (req: any, res: any) => {
    const { status } = req.body;
    let message = '店铺更新成功';
    if (status) {
      message = `店铺已${status === 'enable' ? '启用' : '禁用'}`;
    }
    res.json({
      code: 200,
      data: {},
      message,
    });
  },
};
