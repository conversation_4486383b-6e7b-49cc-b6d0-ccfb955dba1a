import React, { useEffect, useState } from 'react';
import { Button, Checkbox, Col, List, Row, Space, Tag, message, Modal } from 'antd';
import { DeleteOutlined, EditOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { getRoleList,  updateRole, addRole, deleteRole, RoleItem } from '@/services/ibidder_api/user';
import styles from './style.less';
import AddOrEditRoleModal from './components/AddOrEditRoleModal';

interface RoleManagementProps {
  isActive: boolean;
}

const RoleManagement: React.FC<RoleManagementProps> = ({ isActive }) => {
  const [roleList, setRoleList] = useState<RoleItem[]>([]);
  const [activeRole, setActiveRole] = useState<RoleItem | null>(null);
  const [editingRole, setEditingRole] = useState<RoleItem | null>(null); // 单独管理正在编辑的角色
  const [loading, setLoading] = useState(false);
  const [permissions, setPermissions] = useState<any>({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<'add' | 'edit'>('add');
  const [modalLoading, setModalLoading] = useState(false);

  const permissionMap = [
    { category: '子账号管理', key: 'account_management_perm', admin: true, actions: [{ label: '管理', value: 'MANAGE' }] },
    { category: '角色管理', key: 'role_management_perm', admin: true, actions: [{ label: '管理', value: 'MANAGE' }] },
    // { category: '分组管理', key: 'group_management_perm', actions: [{ label: '管理', value: 'MANAGE' }] },
    { category: '店铺授权', key: 'shop_auth_perm', actions: [{ label: '管理', value: 'MANAGE' }] },
    { category: '操作日志', key: 'operation_log_perm', actions: [{ label: '查看', value: 'VIEW' }, { label: '管理', value: 'MANAGE' }] },
    { category: '广告设置', key: 'ai_switch_perm', actions: [{ label: 'AI广告执行开关', value: 'MANAGE' }] },
  ];
  
  const fetchRoles = () => {
    setLoading(true);
    getRoleList().then((res) => {
      if (res.code === 200) {
        setRoleList(res.data.items);
        if (res.data.items.length > 0) {
          if (activeRole) {
            setActiveRole(res.data.items.find((item: RoleItem) => item.id === activeRole.id) || res.data.items[0]);
            setPermissionsFromRole(res.data.items.find((item: RoleItem) => item.id === activeRole.id) || res.data.items[0]);
          } else {
            setActiveRole(res.data.items[0]);
            setPermissionsFromRole(res.data.items[0]);
          }
        }
      }
    }).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    if (isActive) {
      fetchRoles();
    }
  }, [isActive]);

  const setPermissionsFromRole = (role: RoleItem) => {
    const perms: any = {};
    permissionMap.forEach(p => {
      perms[p.key] = role[p.key] ? [role[p.key]] : [];
    });
    setPermissions(perms);
  };

  const handleRoleClick = (role: RoleItem) => {
    setActiveRole(role);
    setPermissionsFromRole(role);
  };

  const handleAddRoleClick = () => {
    setModalType('add');
    setEditingRole(null); // 清空正在编辑的角色
    setIsModalOpen(true);
  };

  const handleEditRole = (role: RoleItem) => {
    setModalType('edit');
    setEditingRole(role); // 使用专门的状态来存储正在编辑的角色
    setIsModalOpen(true);
  };

  const handleDeleteRole = (role: RoleItem) => {
    Modal.confirm({
      title: '删除角色',
      icon: <ExclamationCircleFilled />,
      content: `确定删除角色【${role.name}】吗？`,
      okText: '删除',
      cancelText: '取消',
      onOk() {
        return deleteRole({ id: role.id }).then((res) => {
          if (res.code === 200) {
            message.success('删除成功');
            fetchRoles();
          } else {
            message.error(res.message || '删除失败');
          }
        });
      },
    });
  };

  const handlePermissionChange = (permKey: string, checkedValues: string[]) => {
    const latestValue = checkedValues.filter(v => !((permissions[permKey] || []) as string[]).includes(v));
    const finalValue = latestValue.length > 0 ? latestValue : [];
    setPermissions({ ...permissions, [permKey]: finalValue });
  };
  
  const handleSave = () => {
    if (!activeRole) return;
    const params: any = {
      id: activeRole.id,
      ...permissions,
    };
    // 将数组转为字符串
    Object.keys(params).forEach(key => {
      if (Array.isArray(params[key])) {
        params[key] = params[key][0] || 'NO_PERMISSION';
      }
    });

    updateRole(params).then(res => {
      if (res.code === 200) {
        message.success('保存成功');
        fetchRoles();
      } else {
        message.error(res.message || '保存失败');
      }
    });
  };

  const handleModalOk = (values: { name: string }) => {
    setModalLoading(true);
    const apiCall =
      modalType === 'add'
        ? addRole(values)
        : updateRole({ id: editingRole!.id, name: values.name }); // 使用 editingRole

    apiCall
      .then((res) => {
        if (res.code === 200) {
          message.success(`${modalType === 'add' ? '新增' : '修改'}成功`);
          setIsModalOpen(false);
          fetchRoles();
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        setModalLoading(false);
      });
  };

  return (
    <Row className={styles.roleManagement}>
      <Col span={6}>
        <div className={styles.roleListContainer}>
          <div className={styles.roleListHeader}>
            <span>角色列表</span>
            <Button type="primary" onClick={handleAddRoleClick}>+ 新增角色</Button>
          </div>
          <List
            className={styles.roleList}
            loading={loading}
            dataSource={roleList}
            renderItem={(item) => (
              <List.Item
                onClick={() => handleRoleClick(item)}
                className={`${styles.roleItem} ${activeRole?.id === item.id ? styles.active : ''}`}
              >
                <span>{item.name}</span>
                <Space>
                  {item.type === 1 ? (
                    <Tag color="success">系统预设</Tag>
                  ) : (
                    <>
                      <EditOutlined
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditRole(item);
                        }}
                      />
                      <DeleteOutlined
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteRole(item);
                        }}
                      />
                    </>
                  )}
                </Space>

              </List.Item>
            )}
          />
        </div>
      </Col>
      <Col span={18}>
        {activeRole && (
          <div className={styles.permissionContainer}>
            <div className={styles.permissionHeader}>
              {activeRole.name} - 功能权限
            </div>
            <div className={styles.permissionContent}>
              <Row className={styles.permissionRow} style={{ background: '#F2F3F5' }}>
                <Col span={12} className={styles.permissionCategory}>
                  权限
                </Col>
                <Col span={12}>
                  设置
                </Col>
              </Row>
              {permissionMap
                .filter(perm => activeRole.type === 1 || !perm.admin)
                .map((perm) => (
                <Row
                  key={perm.key}
                  className={styles.permissionRow}
                >
                  <Col span={12} className={styles.permissionCategory}>
                    {perm.category}
                  </Col>
                  <Col span={12}>
                    <Checkbox.Group
                      value={permissions[perm.key]}
                      onChange={(checkedValues) => handlePermissionChange(perm.key, checkedValues)}
                      disabled={activeRole.type === 1}
                    >
                      <Space>
                        {perm.actions.map((action) => (
                          <Checkbox key={action.value} value={action.value}>{action.label}</Checkbox>
                        ))}
                      </Space>
                    </Checkbox.Group>
                  </Col>
                </Row>
              ))}
            </div>
            <div className={styles.permissionFooter}>
              <Button 
                type="primary" 
                onClick={handleSave}
                disabled={activeRole.type === 1}
              >
                保存
              </Button>
            </div>
          </div>
        )}
      </Col>
      <AddOrEditRoleModal
        open={isModalOpen}
        onCancel={() => {
          setIsModalOpen(false);
          setEditingRole(null); // 关闭时清空
        }}
        onOk={handleModalOk}
        loading={modalLoading}
        type={modalType}
        initialData={modalType === 'edit' ? { name: editingRole?.name || '' } : undefined} // 使用 editingRole
      />
    </Row>
  );
};

export default RoleManagement;
