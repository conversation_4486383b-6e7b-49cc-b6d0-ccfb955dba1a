import React from 'react';
import { Modal, Typography, Tabs, Space } from 'antd';
import type { TabsProps } from 'antd';
import DailyDataTab from './DailyDataTab';
import ComparativeAnalysisTab from './ComparativeAnalysisTab';
import AdPlacementTab from './AdPlacementTab';

const { Title, Text } = Typography;
interface CampaignDetailModalProps {
  visible: boolean;
  onCancel: () => void;
  campaignData: {
    campaign_id: number;
    campaign_type: string;
    campaign_name?: string;
  };
  tab: "ad_place" | '';
}

const CampaignDetailModal: React.FC<CampaignDetailModalProps> = ({
  visible,
  onCancel,
  campaignData,
  tab,
}) => {
  if (!campaignData) {
    return null;
  }

  const items: TabsProps['items'] = [
    {
      key: 'daily_data',
      label: '天数据',
      children: (
        <DailyDataTab
          campaignId={campaignData.campaign_id}
          campaign_type={campaignData.campaign_type}
        />
      ),
    },
    {
      key: 'sequential_analysis',
      label: '对比分析',
      children: (
        <ComparativeAnalysisTab
          campaignId={campaignData.campaign_id}
          campaignType={campaignData.campaign_type}
        />
      ),
    },
  ];

  if (campaignData.campaign_type === 'sp' || campaignData.campaign_type === 'sb') {
    items.push({
      key: 'ad_place',
      label: '广告位',
      children: (
        <AdPlacementTab
          campaignId={campaignData.campaign_id}
          campaign_type={campaignData.campaign_type}
        />
      ),
    });
  }

  return (
    <Modal
      title={
        <Title level={5} style={{ margin: 0 }}>
          <Space>
            <span>广告活动详情</span>
            <Text type="secondary">{campaignData.campaign_name}</Text>
          </Space>
        </Title>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={1200}
      destroyOnClose
      destroyOnHidden
    >
      <Tabs defaultActiveKey={tab ?? 'daily_data'} items={items} />
    </Modal>
  );
};

export default CampaignDetailModal;