import React from 'react';
import { Flex, Typography } from 'antd';
import IMGcustomizing from '@/assets/images/customizing.svg';

const { Text } = Typography;

interface GeneratingStateProps {
  /** 生成状态显示的文本 */
  text?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const GeneratingState: React.FC<GeneratingStateProps> = ({
  text = '正在生成报告中，请稍候...',
  style
}) => {
  return (
    <Flex
      align='center'
      style={{
        flexDirection: 'column',
        gap: 16,
        marginTop: '10vh',
        padding: '0 24px',
        ...style
      }}
    >
      <img src={IMGcustomizing} />
      <Text style={{ fontSize: '16px', textAlign: 'center' }}>{text}</Text>
    </Flex>
  );
};

export default GeneratingState;