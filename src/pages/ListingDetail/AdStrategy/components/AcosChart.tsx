import React from 'react';
import ReactECharts from 'echarts-for-react';

interface AcosChartProps {
  data: { date: string; acos: string }[];
}

const AcosChart: React.FC<AcosChartProps> = ({ data }) => {
  const option = {
    xAxis: {
      type: 'category',
      data: data.map(item => item.date),
      show: false,
    },
    yAxis: {
      type: 'value',
      show: false,
      splitLine: { show: false },
    },
    series: [
      {
        data: data.map(item => parseFloat(item.acos)),
        type: 'line',
        smooth: true,
        showSymbol: false,
        lineStyle: {
          width: 2,
          color: '#FFD66B'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(255, 214, 107, 0.5)' // 渐变起始颜色
            }, {
              offset: 1, color: 'rgba(255, 214, 107, 0)' // 渐变结束颜色
            }]
          }
        },
        // markPoint: {
        //   data: [
        //     {
        //       type: 'max',
        //       name: '最大值',
        //     }
        //   ],
        //   symbol: 'circle',
        //   symbolSize: 6,
        //   label: {
        //     show: true,
        //     formatter: '{c}%',
        //     position: 'top',
        //     color: '#333',
        //     fontSize: 10
        //   }
        // }
      },
    ],
    grid: {
      left: 0,
      right: 0,
      top: 5,
      bottom: 5,
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.65)', // 黑色背景
      borderColor: '#444',
      textStyle: {
        color: '#fff', // 白色文字
      },
      formatter: (params: any) => {
        const item = params[0];
        return `${item.axisValue}<br/>ACoS: ${item.value}%`;
      },
      appendTo: 'body',
    }
  };

  return <ReactECharts option={option} style={{ width: '100%', height: '100%' }} notMerge={true} lazyUpdate={true} />;
};

export default AcosChart;
