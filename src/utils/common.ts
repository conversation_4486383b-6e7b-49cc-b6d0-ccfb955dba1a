/**
 * 根据site获取对应的货币符号
 * @param site 站点代码
 * @returns 货币符号
 */
export const getSiteCurrency = (site: string): string => {
  const currencyMap: Record<string, string> = {
    // 北美
    'US': '$', // 美元
    'CA': 'C$', // 加拿大元
    'MX': 'MX$', // 墨西哥比索
    
    // 欧洲
    'UK': '£', // 英镑
    'GB': '£', // 英镑
    'EU': '€', // 欧元
    'DE': '€', // 德国欧元
    'FR': '€', // 法国欧元
    'IT': '€', // 意大利欧元
    'ES': '€', // 西班牙欧元
    
    // 亚洲
    'CN': '¥', // 人民币
    'JP': '¥', // 日元
    'KR': '₩', // 韩元
    'SG': 'S$', // 新加坡元
    'IN': '₹', // 印度卢比
    
    // 大洋洲
    'AU': 'A$', // 澳大利亚元
    'NZ': 'NZ$', // 新西兰元
    
    // 其他
    'BR': 'R$', // 巴西雷亚尔
    'RU': '₽', // 俄罗斯卢布
    'ZA': 'R', // 南非兰特
  };
  
  // 将site转换为大写，以适配不同大小写的输入
  const upperSite = site.toUpperCase();
  
  // 返回对应的货币符号，如果找不到则返回默认值'$'
  return currencyMap[upperSite] || '$';
};

export const sourceImageUrl = (url: string) => {
  return `https://ads-web.oss-cn-hangzhou.aliyuncs.com/images/${url}`;
};

// 添加处理亚马逊图片URL的函数
export const getResizedAmazonImageUrl = (url: string, size: number = 160): string => {
  if (!url) return '';

  // 尝试替换常见的亚马逊图片尺寸参数
  return url
    // .replace(/_(SL|SX|SY)\d+_ /g, `_SL${size}_`)  // 替换 _SL80_, _SX80_, _SY80_ 等格式
    // .replace(/\._\w+(\d+)_\./g, `._SL${size}_`)  // 替换其他可能的格式
    // .replace(/\/(I|P)\/(\w+?)(\.|-)[_\w]*\./g, `/$1/$2.`) // 移除中间尺寸标记
    .replace(/\.(jpg|png|jpeg)/i, `._SL${size}_.$1`); // 添加尺寸标记
};

export const AiOptionList = [
  {
    name: '发布期',
    value: '【广告目标】\n这是一款全新产品，我需要尽快获得曝光和点击，来测试市场的反应和收集关键词数据\n\n【核心指令】\n预算要给足: 请将每日总预算设置为周策略范围的上限，确保自动和广泛广告有足够的“子弹”去进行探索。\n出价要大胆: 请为这两个探索性的广告活动设置一个较高的基础竞价系数，目标是确保我的新产品能在用户面前展示出来，不必过于担心初期的ACOS。\n全面撒网: 请AI在进行分时出价(HoD)时，不要过早地大幅降低任何时段的出价，我们需要全天候的数据。\n\n【明确限制】\n虽然要大胆，但请确保所有广告位的新竞价(new_bid)不要超过一个离谱的水平，例如9.00美元。\n\n【对标竞品】\nASIN：\nASIN：\nASIN：',
  },
  {
    name: '成长期',
    value: '【广告目标】\n我的这款产品已经开始起量，评价和转化率都在稳步提升，我需要乘胜追击，最大化订单量。\n\n【核心指令】\n火力全开攻主将: 请将“核心主推词-精准匹配”这个广告活动的预算，在它日均花费的基础上至少增加50%。这是首要任务，必须确保它24小时在线。\n增强侵略性: 同时，请将该广告活动的基础竞价上调10%-15%，以获取在搜索结果顶部的绝对优势。\n调兵遣将: 可以适度削减品牌推广视频广告的预算，将资源集中到我们的增长引擎上。\n\n【明确限制】\n日总预算可以上调，但请严格控制在周策略设定的预算范围之内。\n\n【对标竞品】\nASIN：\nASIN：\nASIN：',
  },
  {
    name: '成熟期',
    value: '【广告目标】\n这款产品已经是稳定贡献利润的现金牛，我的目标是进一步降低ACOS，提升利润空间。\n\n【核心指令】\n精准手术: 请对SD广告“关联商品投放”进行预算削减，幅度不低于40%，并将节省下来的预算100%转移给SP广告“长尾词-精准”。SP广告“高流量词”和SP广告“高转化词”这两个为核心出单请确保优先分配预算。\n优化时间: 在进行分时出价(HoD)调整时，请对凌晨0点至6点设置大幅度的负向出价系数。\n降本增效: 在进行广告位调整时，如果“详情页”广告位的ACOS显著高于其他位置，请降低其出价。\n\n【明确限制】\n总预算严格保持不变，我只需要进行内部的预算重分配。\n\n【对标竞品】\nASIN：\nASIN：\nASIN：',
  },
  {
    name: '衰退期/清库存',
    value: '【广告目标】\n这是我的旧款产品，我需要尽快将剩余库存清空。\n\n【核心指令】\n清仓甩卖: 只要能带来订单，我就能接受比平时高得多的ACOS。请为所有还能出单的、与此产品相关的广告活动增加预算。\n提升可见度: 为这些广告活动适度提高基础竞价，确保它们在生命周期的最后阶段能被更多人看到。\n\n【明确限制】\n我的核心目标是“出单”，不是“利润”。请AI在优化时，将“订单量”作为最高优先级，暂时放宽对ACOS的严格要求。\n\n【对标竞品】\nASIN：\nASIN：\nASIN：',
  },
  {
    name: '激活期',
    value: '【广告目标】\n我的这款明星产品升级后重新上架，我需要快速恢复它之前的广告表现和市场地位。\n\n【核心指令】\n重启引擎: 请重点关注并增加“明星款-词组匹配”和“明星款-自动”这两个历史功勋广告的预算和基础竞价。\n测试新特性: 同时，我开设了一个新的SP广告“明星款-新功能词”，用于测试与本次升级相关的新关键词，也请为其分配一定的探索预算。\n借鉴历史: 请AI在制定分时和广告位策略时，重点参考该产品在下架前那几周的“同天”历史数据，因为那是最有价值的参考。\n\n【明确限制】\n总预算可以适度增加，但希望ACOS能尽快恢复到之前的健康水平。这是一个兼具“探索”和“恢复”双重任务的阶段。\n\n【对标竞品】\nASIN：\nASIN：\nASIN：',
  },
  {
    name: '促销期',
    value: '【广告目标】\n采取“激进型(aggressive)”策略，全力冲刺Prime Day的销售目标！\n\n【核心指令】\n预算充足: 请将今日总预算设置为周策略预算范围的最高值，确保弹药充足。\n分时重点打击: 这是最重要的指令！在进行分时出价(HoD)调整时，请在【上午10点-下午2点】和【晚上8点-晚上11点】这两个时间段内，设置非常积极的出价系数（例如，上调15%-20%）。\n其他时段控制: 在凌晨等非高峰时段，请设置保守的出价系数，以节省预算用于关键时刻。\n全线猛攻: 所有与本次活动商品相关的广告活动，其基础竞价都可以适度上调，以应对激烈的竞争。\n\n【明确限制】\n尽管是活动日，但请确保所有调整后的广告位新竞价(new_bid)不要超过9.00美元。\n除了我提到的高峰时段，其他时间段的出价调整请保持理智。\n\n【对标竞品】\nASIN：\nASIN：\nASIN：',
  },
];

export const AiOptionLabelList = [
  '广告目标',
  '核心指令',
  '明确限制',
  '对标竞品',
];

/**
 * 根据差距值获取颜色
 * @param diffStr 差距值
 * @returns 颜色
 */
export const getDiffColor = (diffStr: string): string => {
  // 解析差距值，支持正负数和百分比
  const match = diffStr.match(/([+-]?\d+(?:\.\d+)?)/);
  if (match) {
    const value = parseFloat(match[1]);
    if (value > 0) {
      return '#ff4d4f'; // 红色：比目标值大（不好）
    } else if (value < 0) {
      return '#52c41a'; // 绿色：比目标值小（好）
    }
  }
  return 'inherit'; // 默认黑色
};

export const genericSorter = (a: any, b: any, key: string) => {
  let valA = a?.[key];
  let valB = b?.[key];

  // Handle cases where values might be null or undefined
  const valAExists = valA !== null && valA !== undefined && valA !== '';
  const valBExists = valB !== null && valB !== undefined && valB !== '';

  if (!valAExists && !valBExists) return 0;
  if (!valAExists) return -1;
  if (!valBExists) return 1;

  // Attempt to convert to numbers for comparison
  const numA = parseFloat(String(valA).replace(/[^0-9.-]+/g, ''));
  const numB = parseFloat(String(valB).replace(/[^0-9.-]+/g, ''));

  if (!isNaN(numA) && !isNaN(numB)) {
    if (numA < numB) return -1;
    if (numA > numB) return 1;
    return 0;
  }

  // Fallback to string comparison if conversion fails
  return String(valA).localeCompare(String(valB));
};

/** 图标的颜色 */
export const chartColors = ['#5BC4FF', '#FFD66B', '#AE8FF7', '#1DB88C', '#FF8F6B', '#5B93FF']