import React, { useState } from 'react';
import { Button, Input } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

interface ConfirmationSectionProps {
  /** 当前文本区域的值 */
  value?: string;
  /** 文本区域变化处理函数 */
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  /** 审核回调函数 */
  onApprove?: () => void;
  /** 修改回调函数 */
  onModify?: () => void;
  /** 是否禁用按钮 */
  disabled?: boolean;
  /** 文本区域最小行数 */
  minRows?: number;
  /** 文本区域最大行数 */
  maxRows?: number;
  /** 文本区域最大宽度 */
  maxWidth?: number | string;
  /** 按钮大小 */
  buttonSize?: 'small' | 'middle' | 'large';
  /** 文本区域提示语 */
  placeholder: string;
  /** 是否为审核模式，为true时不显示审核按钮 */
  report_status: boolean;
}

const ConfirmationSection: React.FC<ConfirmationSectionProps> = ({
  value = '',
  onChange,
  onApprove,
  onModify,
  disabled = false,
  minRows = 5,
  maxRows = 8,
  maxWidth = '100%',
  buttonSize = 'large',
  placeholder = '请输入修改意见',
  report_status = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelClick = () => {
    setIsEditing(false);
    // 清除输入内容
    if (onChange) {
      onChange({ target: { value: '' } } as React.ChangeEvent<HTMLTextAreaElement>);
    }
    // 不调用 onCancel，避免关闭窗口
  };

  const handleModifyClick = () => {
    if (onModify) {
      onModify();
    }
  };

  const handleApproveClick = () => {
    if (onApprove) {
      onApprove();
    }
  };

  const isSubmitDisabled = disabled || !value || value.trim().length === 0;

  return (
    <div className='confirmBtn'>
      {!isEditing ? (
        // 初始状态：显示修改和审核按钮
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '8px' }}>
          <Button
            size={buttonSize}
            style={{ width: '100px' }}
            onClick={handleEditClick}
            disabled={disabled}
          >
            修改
          </Button>
          {!report_status && (
            <Button
              type="primary"
              size={buttonSize}
              style={{ width: '200px' }}
              onClick={handleApproveClick}
              disabled={disabled}
            >
              审核
            </Button>
          )}
        </div>
      ) : (
        // 编辑状态：显示输入框和取消/提交按钮
        <>
          <div >
            <Input.TextArea
              autoSize={{ minRows, maxRows }}
              style={{ width: '100%', maxWidth }}
              placeholder={placeholder}
              value={value}
              onChange={onChange}
              autoFocus
            />
          </div>

          <div style={{ display: 'flex', justifyContent: 'space-between', gap: '20px' }}>
            <div style={{
              display: 'flex',
              flex: '1 1 0%',
              alignItems: 'flex-start',
              gap: '4px',
              color: 'rgb(216, 0, 39)',
              padding: '10px',
              background: 'rgb(255, 247, 232)',
              flexDirection: 'row',
              borderRadius: '4px',
            }}>
              <InfoCircleOutlined style={{ margin: '3px 2px 0 3px' }} />
              <div style={{ lineHeight: '1.3' }}>
                为确保AI优化效果，通常不建议手动修改广告活动的预算、竞价、分时竞价和广告位竞价，AI会根据实际投放表现进行动态调整。
                <br />
                仅在有重大策略调整需求时，才考虑手动干预。请注意，调整广告位竞价时，务必以百分比形式（例如：50%）输入。
              </div>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <Button
                size={buttonSize}
                style={{ width: '100px' }}
                onClick={handleCancelClick}
                disabled={disabled}
              >
                取消
              </Button>
              <Button
                type="primary"
                size={buttonSize}
                style={{
                  width: '200px',
                  backgroundColor: isSubmitDisabled ? '#ffccc7' : '#ff4d4f',
                  borderColor: isSubmitDisabled ? '#ffccc7' : '#ff4d4f',
                  color: '#fff'
                }}
                onClick={handleModifyClick}
                disabled={isSubmitDisabled}
              >
                提交修改意见
              </Button>
            </div>

          </div>
        </>
      )}
    </div>
  );
};

export default ConfirmationSection;
