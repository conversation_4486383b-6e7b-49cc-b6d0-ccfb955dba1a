import {
  But<PERSON>,
  Card,
  Col,
  Dropdown,
  Input,
  List,
  Menu,
  Row,
  Select,
  Space,
  Table,
} from 'antd';
import { EllipsisOutlined } from '@ant-design/icons';
import React, { useEffect } from 'react';

interface GroupManagementProps {
  isActive: boolean;
}

const GroupManagement: React.FC<GroupManagementProps> = ({ isActive }) => {
  useEffect(() => {
    if (isActive) {
      // 在这里获取分组管理数据
      console.log('GroupManagement is active, fetching data...');
    }
  }, [isActive]);
  
  const columns = [
    {
      title: '用户昵称',
      dataIndex: 'nickname',
      key: 'nickname',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: '所属分组',
      dataIndex: 'group',
      key: 'group',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: '店铺权限',
      dataIndex: 'shopPermission',
      key: 'shopPermission',
    },
    {
      title: 'ASIN权限',
      dataIndex: 'asinPermission',
      key: 'asinPermission',
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      key: 'operationTime',
    },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space size="middle">
          <a>编辑</a>
          <a>删除</a>
        </Space>
      ),
    },
  ];

  const groupData = ['开结箍 (1)', '沙发除毛刷 (2)', '家具除毛刷 (0)'];

  const menu = (
    <Menu>
      <Menu.Item key="1">编辑</Menu.Item>
      <Menu.Item key="2">删除</Menu.Item>
    </Menu>
  );

  return (
    <Row gutter={24}>
      <Col span={6}>
        <Card
          title="分组列表"
          extra={
            <Button type="primary" >
              + 创建分组
            </Button>
          }
        >
          <List
            dataSource={groupData}
            renderItem={(item) => (
              <List.Item
                actions={[
                  <Dropdown overlay={menu} trigger={['click']}>
                    <EllipsisOutlined />
                  </Dropdown>,
                ]}
              >
                {item}
              </List.Item>
            )}
          />
        </Card>
      </Col>
      <Col span={18}>
        <Row justify="end" style={{ marginBottom: '16px' }}>
          <Col>
            <Space>
              <Select defaultValue="用户状态" style={{ width: 120 }} />
              <Select defaultValue="批量操作" style={{ width: 120 }} />
              <Input.Search
                placeholder="用户昵称"
                onSearch={() => {}}
                style={{ width: 200 }}
              />
              <Button type="primary">
                分配成员
              </Button>
            </Space>
          </Col>
        </Row>
        <Table
          columns={columns}
          dataSource={[]}
          pagination={{ total: 0, showTotal: (total) => `共 ${total} 条` }}
        />
      </Col>
    </Row>
  );
};

export default GroupManagement;
