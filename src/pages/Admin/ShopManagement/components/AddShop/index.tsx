import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Select, message, Radio, Checkbox, Button, Space } from 'antd';
import { getAuthLink } from '@/services/ibidder_api/shop';
import styles from './style.less';
import { sourceImageUrl } from '@/utils/common';
interface ShopData {
  market: string;
  country_code: string[];
  shop_name: string;
}

interface AddShopModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  initialStep?: number;
  initialValues?: ShopData;
}

const AddShopModal: React.FC<AddShopModalProps> = ({ open, onCancel, onOk, initialStep = 1, initialValues }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(initialStep);
  const [authUrl, setAuthUrl] = useState('');
  const [isChecked, setIsChecked] = useState(false);

  useEffect(() => {
    setStep(initialStep);
    if (initialStep === 2 && initialValues) {
      form.setFieldsValue(initialValues);
      handleGetAuthLink(initialValues);
    } else {
      form.resetFields();
    }
  }, [initialStep, initialValues, open]);

  const marketOptions = [
    { label: '北美', value: 'NA' },
    { label: '欧洲', value: 'EU' },
    { label: '亚太', value: 'FE' },
  ];

  const countryOptionsByMarket = {
    'NA': [
      { label: '美国', value: 'US' }, { label: '加拿大', value: 'CA' },
      { label: '墨西哥', value: 'MX' }, { label: '巴西', value: 'BR' }
    ],
    'EU': [
      { label: '英国', value: 'UK' }, { label: '德国', value: 'DE' },
      { label: '法国', value: 'FR' }, { label: '意大利', value: 'IT' },
      { label: '西班牙', value: 'ES' }, { label: '荷兰', value: 'NL' },
      { label: '瑞典', value: 'SE' }, { label: '波兰', value: 'PL' },
      { label: '比利时', value: 'BE' }, { label: '沙特', value: 'SA' },
      { label: '阿联酋', value: 'AE' }, { label: '埃及', value: 'EG' },
      { label: '土耳其', value: 'TR' }, { label: '印度站', value: 'IN' }
    ],
    'FE': [
      { label: '日本站', value: 'JP' }, { label: '澳大利亚', value: 'AU' },
      { label: '新加坡', value: 'SG' }
    ],
  };
  
  const [selectedMarket, setSelectedMarket] = useState<keyof typeof countryOptionsByMarket>('NA');

  const handleGetAuthLink = async (values: ShopData) => {
    try {
      setLoading(true);
      const result: any = await getAuthLink({
        market: values.market,
        country_code_list: values.country_code,
        shop_name: values.shop_name,
      });

      if (result.code === 200) {
        setAuthUrl(result.data.url);
        setStep(2);
      } else {
        message.error(result.message || '获取授权链接失败');
      }
    } catch (error) {
      console.error('获取授权链接出错:', error);
    } finally {
      setLoading(false);
    }
  }

  const handleNextStep = async () => {
    try {
      const values = await form.validateFields();
      handleGetAuthLink(values);
    } catch (error) {
      // 验证失败时，Antd Form 会自动处理提示，这里不需要额外操作
    }
  };

  const handleCopy = () => {
    if (!authUrl) return;
    const textArea = document.createElement('textarea');
    textArea.value = authUrl;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      message.success('复制成功');
    } catch (err) {
      message.error('复制失败');
    }
    document.body.removeChild(textArea);
  };

  const handleAuthorize = () => {
    if (isChecked) {
      window.location.href = authUrl;
      handleCancel();
    } else {
      message.warning('请先勾选确认框');
    }
  };
  
  const handleCancel = () => {
    form.resetFields();
    setStep(1);
    setAuthUrl('');
    setIsChecked(false);
    onCancel();
  };

  const renderStepOne = () => (
    <Modal
      title="店铺信息填写"
      open={open}
      onOk={handleNextStep}
      onCancel={handleCancel}
      confirmLoading={loading}
      okText="继续"
      width={1280}
    >
      <Form form={form} layout="horizontal" style={{ marginTop: 24 }}>
        <Form.Item
          name="market"
          label="选择授权市场"
          rules={[{ required: true, message: '请选择市场' }]}
          initialValue={selectedMarket}
          className={styles.formItem}
        >
          <Radio.Group
            onChange={(e) => {
              const newMarket = e.target.value;
              setSelectedMarket(newMarket);
              form.setFieldsValue({ country_code: [] });
            }}
            value={selectedMarket}
          >
            {marketOptions.map(option => (
              <Radio key={option.value} value={option.value} style={{ width: 80 }}>{option.label}</Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="country_code"
          label="选择运营国家"
          rules={[{ required: true, message: '请选择国家' }]}
          className={styles.formItem}
        >
          <Checkbox.Group style={{ width: '100%' }}>
            {Object.entries(countryOptionsByMarket).map(([market, countries]) => (
              <div key={market} style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                <div>
                  {countries.map(country => (
                    <Checkbox
                      key={country.value}
                      value={country.value}
                      disabled={selectedMarket !== market}
                      style={{ width: 90, marginBottom: 8 }}
                    >
                      {country.label}
                    </Checkbox>
                  ))}
                </div>
              </div>
            ))}
          </Checkbox.Group>
        </Form.Item>
        <Form.Item
          name="shop_name"
          label="亚马逊店铺名称"
          rules={[{ required: true, message: '请输入店铺名称' }]}
          className={styles.formItem}
        >
          <Input placeholder="如: AnkerDriect" autoComplete="off" style={{ width: 300 }} />
        </Form.Item>
      </Form>
    </Modal>
  );

  const renderStepTwo = () => (
    <Modal
      title="亚马逊广告授权"
      open={open}
      onCancel={handleCancel}
      footer={[
        <Button key="back" onClick={handleCancel}>取消</Button>,
        <Button key="submit" type="primary" onClick={handleAuthorize} disabled={!isChecked}>
          去授权
        </Button>,
      ]}
      width={1280}
    >
      <div style={{ display: 'flex', gap: '40px', marginTop: 24 }}>
        <div style={{ flex: 1 }}>
          <h3 style={{ fontWeight: 'bold' }}>步骤1:亚马逊设置</h3>
          <p>点击“去授权”跳转到亚马逊卖家平台登录</p>
          <p style={{ color: '#FA8C16' }}>如已登陆其他卖家账号，请前往销售易再重复以上操作；</p>
          <p style={{ color: '#FA8C16' }}>或者直接复制以下链接，前往安全的浏览器打开并授权。</p>
          <Space>
            授权链接:
            <Input value={authUrl} readOnly style={{ width: 300 }} />
            <Button onClick={handleCopy} color="primary" variant="outlined">复制</Button>
          </Space>
        </div>
        <div style={{ flex: 1 }}>
          <h3 style={{ fontWeight: 'bold' }}>步骤2:亚马逊广告授权</h3>
          <p>登录成功后，跳转至广告授权页面，确认当前账户无误后，点击“允许”按钮同意授权</p>
          <img src={sourceImageUrl('authorization_tip.png')} alt="Amazon Auth" style={{ width: '80%' }}/>
        </div>
      </div>
      <div style={{ textAlign: 'right', marginTop: 24 }}>
        <Checkbox onChange={(e) => setIsChecked(e.target.checked)}>
          <span style={{ color: '#D80027' }}>确认当前电脑与IP是您授权店铺的常用环境</span>
        </Checkbox>
      </div>
    </Modal>
  );

  return step === 1 ? renderStepOne() : renderStepTwo();
};

export default AddShopModal;
