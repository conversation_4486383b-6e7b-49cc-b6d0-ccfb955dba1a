import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Table, Button, Space, Row, Col, Spin, TableColumnType, Empty, Tag } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useModel } from '@umijs/max';
import ReusableChart from './ReusableChart';
import { getCampaignDailyData } from '@/services/ibidder_api/operation';
import type { DailyChartData, DailyTableRecord, DailyDataResponse } from './types';
import { formatCurrency, getWeekday, countryTimezoneMap } from '@/utils/bus';
import { genericSorter } from '@/utils/common';
import PresetRangePicker from './components/PresetRangePicker';
import { createTitleWithTooltip } from './utils/tooltipConfig';

const dailyDataSeriesConfig = [
  { name: '广告订单', key: 'orders', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '花费', key: 'spend', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '销售额', key: 'sales', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '预算', key: 'budget', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: 'ACoS', key: 'acos', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CVR', key: 'cvr', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CTR', key: 'ctr', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: '曝光量', key: 'impressions', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '点击', key: 'clicks', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '点击百分比', key: 'click_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CPC', key: 'cpc', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '搜索结果首页首位IS', key: 'topOfSearchImpressionShare', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: 'CPA', key: 'cpa', type: 'line' as const, yAxisIndex: 0, showSymbol: false },
  { name: '广告销量', key: 'units', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '直接成交订单', key: 'sku_orders', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '直接成交销售额', key: 'sku_sales', type: 'bar' as const, yAxisIndex: 0, barMaxWidth: 30 },
  { name: '花费百分比', key: 'spend_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
  { name: '销售额百分比', key: 'sales_ratio', type: 'line' as const, yAxisIndex: 1, showSymbol: false },
];

const getDailyTableColumns = (currency: string, granularity: 'day' | 'week' | 'month'): TableColumnType<DailyTableRecord>[] => [
  {
    title: '日期', width: granularity === 'week' ? 210 : 170, fixed: 'left' as const, dataIndex: 'date', key: 'date',
    render: (value: any, record) => {
      const date = dayjs(record.date);
      if (granularity === 'week') {
        const endDate = date.add(6, 'day');
        return `${date.format('YYYY-MM-DD')}～${endDate.format('YYYY-MM-DD')}`;
      } else if (granularity === 'month') {
        return date.format('YYYY-M月');
      } else {
        const weekday = getWeekday(record.weekday);
        if (weekday) {
          // 判断是否为周末（周六、周日）
          const isWeekend = record.weekday === 'Sat' || record.weekday === 'Sun';
          return (
            <Space>
              <Tag color={isWeekend ? 'orange' : 'blue'}>{weekday}</Tag>
              <span>{record.date}</span>
            </Space>
          );
        }
        return record.date;
      }
    },
    sorter: (a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf(),
    defaultSortOrder: 'descend' as const,
  },
  {
    title: createTitleWithTooltip('广告订单', 'orders'),
    width: 128, dataIndex: 'orders', key: 'orders', sorter: (a, b) => genericSorter(a, b, 'orders'), align: 'right'
  },
  {
    title: createTitleWithTooltip('预算', 'budget'),
    width: 118,
    dataIndex: 'budget',
    key: 'budget',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'budget'),
    align: 'right'
  },
  {
    title: createTitleWithTooltip('花费', 'spend'),
    width: 128,
    dataIndex: 'spend',
    key: 'spend',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'spend'),
    align: 'right'
  },
  {
    title: createTitleWithTooltip('销售额', 'sales'),
    width: 138,
    dataIndex: 'sales',
    key: 'sales',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'sales'),
    align: 'right'
  },
  {
    title: createTitleWithTooltip('ACoS', 'acos'),
    width: 118, dataIndex: 'acos', key: 'acos', sorter: (a, b) => genericSorter(a, b, 'acos'), align: 'right'
  },
  {
    title: createTitleWithTooltip('CVR', 'cvr'),
    width: 118, dataIndex: 'cvr', key: 'cvr', sorter: (a, b) => genericSorter(a, b, 'cvr'), align: 'right'
  },
  {
    title: createTitleWithTooltip('CTR', 'ctr'),
    width: 118, dataIndex: 'ctr', key: 'ctr', sorter: (a, b) => genericSorter(a, b, 'ctr'), align: 'right'
  },
  {
    title: createTitleWithTooltip('曝光量', 'impressions'),
    width: 118, dataIndex: 'impressions', key: 'impressions', sorter: (a, b) => genericSorter(a, b, 'impressions'), align: 'right'
  },
  {
    title: createTitleWithTooltip('点击', 'clicks'),
    width: 118, dataIndex: 'clicks', key: 'clicks', sorter: (a, b) => genericSorter(a, b, 'clicks'), align: 'right'
  },
  {
    title: createTitleWithTooltip('点击百分比', 'click_ratio'),
    width: 143, dataIndex: 'click_ratio', key: 'click_ratio', sorter: (a, b) => genericSorter(a, b, 'click_ratio'), align: 'right'
  },
  {
    title: createTitleWithTooltip('CPC', 'cpc'),
    width: 118,
    dataIndex: 'cpc',
    key: 'cpc',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'cpc'),
    align: 'right'
  },
  {
    title: createTitleWithTooltip(<span>搜索结果<br />首页首位IS</span>, 'topOfSearchImpressionShare'),
    width: 148, dataIndex: 'topOfSearchImpressionShare', key: 'topOfSearchImpressionShare', sorter: (a, b) => genericSorter(a, b, 'topOfSearchImpressionShare'),
    render: (value: any) => value === 0 ? '-' : value < 5 ? '< 5%' : value + ' %',
    align: 'right'
  },
  {
    title: createTitleWithTooltip('CPA', 'cpa'),
    width: 118,
    dataIndex: 'cpa',
    key: 'cpa',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'cpa'),
    align: 'right'
  },
  {
    title: createTitleWithTooltip('广告销量', 'units'),
    width: 128, dataIndex: 'units', key: 'units', sorter: (a, b) => genericSorter(a, b, 'units'), align: 'right'
  },
  {
    title: createTitleWithTooltip(<span>直接<br />成交订单</span>, 'sku_orders'),
    width: 133, dataIndex: 'sku_orders', key: 'sku_orders', sorter: (a, b) => genericSorter(a, b, 'sku_orders'), align: 'right'
  },
  {
    title: createTitleWithTooltip(<span>直接<br />成交销售额</span>, 'sku_sales'),
    width: 148,
    dataIndex: 'sku_sales',
    key: 'sku_sales',
    render: (value: any) => formatCurrency(value, currency),
    sorter: (a, b) => genericSorter(a, b, 'sku_sales'),
    align: 'right'
  },
  {
    title: createTitleWithTooltip('花费百分比', 'spend_ratio'),
    width: 138, dataIndex: 'spend_ratio', key: 'spend_ratio', sorter: (a, b) => genericSorter(a, b, 'spend_ratio'), align: 'right'
  },
  {
    title: createTitleWithTooltip('销售额百分比', 'sales_ratio'),
    width: 158, dataIndex: 'sales_ratio', key: 'sales_ratio', sorter: (a, b) => genericSorter(a, b, 'sales_ratio'), align: 'right'
  },
];
interface DailyDataTabProps {
  campaignId?: number;
  campaign_type?: string;
}

const DailyDataTab: React.FC<DailyDataTabProps> = ({ campaignId, campaign_type }) => {
  const { productInfo } = useModel('productInfo');
  const profileId = productInfo?.profile_id;
  const currency = productInfo?.currency || '';
  const country = (productInfo?.country || '').toLowerCase();
  const tz = countryTimezoneMap[country as keyof typeof countryTimezoneMap];
  const now = tz ? dayjs().tz(tz) : dayjs();
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [chartKey, setChartKey] = useState(0);

  const [tableData, setTableData] = useState<DailyTableRecord[]>([]);
  const [totalData, setTotalData] = useState<DailyDataResponse['total'] | null>(null);
  const [loading, setLoading] = useState(false);
  const [granularity, setGranularity] = useState<'day' | 'week' | 'month'>('day');
  const [dateRange, setDateRange] = useState<[string, string]>([
    now.clone().subtract(13, 'days').format('YYYY-MM-DD'),
    now.clone().format('YYYY-MM-DD'),
  ]);
  const [dateRangeValue, setDateRangeValue] = useState<[Dayjs, Dayjs]>([
    now.clone().subtract(13, 'days'),
    now.clone()
  ]);
  const [selectedSeries, setSelectedSeries] = useState<string[]>(() => dailyDataSeriesConfig.slice(0, 6).map(s => s.name));

  // 检查图表尺寸是否与容器匹配
  const checkChartSize = () => {
    if (!chartContainerRef.current) return false;

    const container = chartContainerRef.current;
    const containerRect = container.getBoundingClientRect();

    // 查找图表元素（通常是 canvas 或 svg）
    const chartElement = container.querySelector('canvas, svg, .echarts-for-react');
    if (!chartElement) return false;

    const chartRect = chartElement.getBoundingClientRect();

    // 允许一定的误差范围（10px）
    const tolerance = 10;
    const widthMatch = Math.abs(containerRect.width - chartRect.width) <= tolerance;
    const heightMatch = Math.abs(containerRect.height - chartRect.height) <= tolerance;
    return widthMatch && heightMatch;
  };

  // 监听容器可见性变化，处理图表尺寸问题
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (chartContainerRef.current) {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && entry.intersectionRatio > 0) {
              // 当容器变为可见时，检查图表尺寸是否匹配
              setTimeout(() => {
                if (!checkChartSize()) {
                  console.log('Chart size mismatch detected, re-rendering...');
                  setChartKey(prev => prev + 1);
                } else {
                  console.log('Chart size is correct, no need to re-render');
                }
              }, 50);
            }
          });
        });

        observer.observe(chartContainerRef.current);
        return () => observer.disconnect();
      }
    };

    return handleVisibilityChange();
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (!campaignId || !profileId || !campaign_type) {
        console.log('[Debug] Aborting fetch: campaignId or profileId is missing.', { campaignId, profileId });
        return;
      }

      setLoading(true);
      try {
        const response = await getCampaignDailyData({
          campaign_id: campaignId,
          profile_id: profileId,
          campaign_type: campaign_type,
          start_date: dateRange[0],
          end_date: dateRange[1],
          group_by: granularity,
        });
        setTableData(response.data?.list || []);
        setTotalData(response.data?.total || null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [campaignId, profileId, dateRange, granularity, campaign_type]);

  const chartData: DailyChartData | null = useMemo(() => {
    if (!tableData || tableData.length === 0) {
      return null;
    }

    const formatDateForChart = (dateStr: string) => {
      const date = dayjs(dateStr);
      if (granularity === 'week') {
        const endDate = date.add(6, 'day');
        return `${date.format('MM-DD')}～${endDate.format('MM-DD')}`;
      } else if (granularity === 'month') {
        return date.format('M月');
      } else {
        return date.format('MM-DD');
      }
    };

    return {
      dates: tableData.map((d) => formatDateForChart(d.date)),
      spend: tableData.map((d) => d.spend),
      sales: tableData.map((d) => d.sales),
      orders: tableData.map((d) => d.orders),
      budget: tableData.map((d) => d.budget),
      acos: tableData.map((d) => parseFloat(d.acos.replace('%', ''))),
      cvr: tableData.map((d) => parseFloat(d.cvr.replace('%', ''))),
      ctr: tableData.map((d) => parseFloat(d.ctr.replace('%', ''))),
      impressions: tableData.map((d) => d.impressions),
      clicks: tableData.map((d) => d.clicks),
      cpc: tableData.map((d) => parseFloat(d.cpc)),
      cpa: tableData.map((d) => parseFloat(d.cpa)),
      units: tableData.map((d) => d.units),
      sku_orders: tableData.map((d) => d.sku_orders),
      sku_sales: tableData.map((d) => d.sku_sales),
      topOfSearchImpressionShare: tableData.map((d) => d.topOfSearchImpressionShare),
      click_ratio: tableData.map((d) => parseFloat(d.click_ratio.replace('%', ''))),
      spend_ratio: tableData.map((d) => parseFloat(d.spend_ratio.replace('%', ''))),
      sales_ratio: tableData.map((d) => parseFloat(d.sales_ratio.replace('%', ''))),
    };
  }, [tableData, granularity]);

  const handleDateChange = (
    dates: null | (Dayjs | null)[] | any,
    dateStrings: [string, string],
  ) => {
    if (dates && dates[0] && dates[1] && dateStrings[0] && dateStrings[1]) {
      setDateRangeValue([dates[0], dates[1]]);
      setDateRange(dateStrings);
    }
  };

  const handleGranularityChange = (gran: 'day' | 'week' | 'month') => {
    setGranularity(gran);
  };

  const handleSelectedSeriesChange = (newSelectedSeries: string[]) => {
    setSelectedSeries(newSelectedSeries);
  };

  return (
    <Spin spinning={loading}>
      <div>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <PresetRangePicker
                value={dateRangeValue}
                onChange={handleDateChange}
              />
              <Space.Compact>
                <Button
                  type={granularity === 'day' ? 'primary' : 'default'}
                  onClick={() => handleGranularityChange('day')}
                >
                  天
                </Button>
                <Button
                  type={granularity === 'week' ? 'primary' : 'default'}
                  onClick={() => handleGranularityChange('week')}
                >
                  周
                </Button>
                <Button
                  type={granularity === 'month' ? 'primary' : 'default'}
                  onClick={() => handleGranularityChange('month')}
                >
                  月
                </Button>
              </Space.Compact>
            </Space>
          </Col>
        </Row>

        <div ref={chartContainerRef} style={{ height: '300px', width: '100%' }}>
          {(chartData && tableData.length > 0) ? (
            <ReusableChart
              key={chartKey}
              currency={currency}
              dates={chartData.dates}
              series={dailyDataSeriesConfig.map(config => ({
                ...config,
                data: chartData[config.key as keyof DailyChartData],
              }))}
              currencyFormattedSeries={['花费', '销售额', '预算', 'CPC', 'CPA', '直接成交销售额']}
              percentageFormattedSeries={['ACoS', 'CVR', 'CTR', '点击百分比', '搜索结果首页首位IS', '花费百分比', '销售额百分比']}
              selectedSeries={selectedSeries}
              onSelectedSeriesChange={handleSelectedSeriesChange}
            />
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          )}
        </div>

        <Table<DailyTableRecord>
          sticky
          style={{ marginTop: 32 }}
          columns={getDailyTableColumns(currency, granularity)}
          dataSource={tableData}
          pagination={{
            total: tableData.length,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
          }}
          scroll={{ x: 'max-content' }}
          summary={() => {
            if (!totalData) return null;
            return (
              <Table.Summary fixed="top">
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>汇总</Table.Summary.Cell>
                  <Table.Summary.Cell index={1} align="right">{totalData.orders}</Table.Summary.Cell>
                  <Table.Summary.Cell index={2} align="right">{formatCurrency(totalData.budget, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={3} align="right">{formatCurrency(totalData.spend, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={4} align="right">{formatCurrency(totalData.sales, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={5} align="right">{totalData.acos}</Table.Summary.Cell>
                  <Table.Summary.Cell index={6} align="right">{totalData.cvr}</Table.Summary.Cell>
                  <Table.Summary.Cell index={7} align="right">{totalData.ctr}</Table.Summary.Cell>
                  <Table.Summary.Cell index={8} align="right">{totalData.impressions}</Table.Summary.Cell>
                  <Table.Summary.Cell index={9} align="right">{totalData.clicks}</Table.Summary.Cell>
                  <Table.Summary.Cell index={10} align="right">-</Table.Summary.Cell>
                  <Table.Summary.Cell index={11} align="right">{formatCurrency(totalData.cpc, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={12} align="right">-</Table.Summary.Cell>
                  <Table.Summary.Cell index={13} align="right">{formatCurrency(totalData.cpa, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={14} align="right">{totalData.units}</Table.Summary.Cell>
                  <Table.Summary.Cell index={15} align="right">{totalData.sku_orders}</Table.Summary.Cell>
                  <Table.Summary.Cell index={16} align="right">{formatCurrency(totalData.sku_sales, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={17} align="right">-</Table.Summary.Cell>
                  <Table.Summary.Cell index={18} align="right">-</Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            );
          }}
        />
      </div>
    </Spin>
  );
};

export default DailyDataTab;
