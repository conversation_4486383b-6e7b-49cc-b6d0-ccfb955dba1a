import React, { useState, useEffect } from 'react';
import { Table, Typography, Space, Spin, TableColumnType } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useModel } from '@umijs/max';
import type {
  SequentialAnalysisData,
  SynchronousAnalysisData,
  SynchronousAnalysisRecord,
} from './types';
import { formatCurrency, getWeekday, countryTimezoneMap } from '@/utils/bus';
import { genericSorter } from '@/utils/common';
import PresetRangePicker from './components/PresetRangePicker';
import styles from './index.less';
import { createTitleWithTooltip } from './utils/tooltipConfig';
import { getCampaignSequentialAnalysis, getCampaignSynchronousAnalysis } from '@/services/ibidder_api/operation';

const { Title } = Typography;

// 将SequentialAnalysisData转换为表格数据
const convertSequentialDataToTableData = (data: SequentialAnalysisData | null) => {
  if (!data) {
    return [];
  }

  return [
    {
      key: 'current',
      period: `${data.current_period.start_date}~${data.current_period.end_date}`,
      orders: data.current_period.total.orders,
      spend: `${data.current_period.total.spend}`,
      sales: `${data.current_period.total.sales}`,
      budget: `${data.current_period.total.budget}`,
      acos: data.current_period.total.acos,
      cvr: data.current_period.total.cvr,
      ctr: data.current_period.total.ctr,
      impressions: data.current_period.total.impressions,
      clicks: data.current_period.total.clicks,
      click_ratio: data.current_period.total.click_ratio || '-',
      cpc: `${data.current_period.total.cpc}`,
      topOfSearchImpressionShare: data.current_period.total.topOfSearchImpressionShare,
      cpa: `${data.current_period.total.cpa}`,
      units: data.current_period.total.units,
      sku_orders: data.current_period.total.sku_orders,
      sku_sales: `${data.current_period.total.sku_sales}`,
      spend_ratio: data.current_period.total.spend_ratio || '-',
      sales_ratio: data.current_period.total.sales_ratio || '-',
    },
    {
      key: 'previous',
      period: `${data.previous_period.start_date}~${data.previous_period.end_date}`,
      orders: data.previous_period.total.orders,
      spend: `${data.previous_period.total.spend}`,
      sales: `${data.previous_period.total.sales}`,
      budget: `${data.previous_period.total.budget}`,
      acos: data.previous_period.total.acos,
      cvr: data.previous_period.total.cvr,
      ctr: data.previous_period.total.ctr,
      impressions: data.previous_period.total.impressions,
      clicks: data.previous_period.total.clicks,
      click_ratio: data.previous_period.total.click_ratio || '-',
      cpc: `${data.previous_period.total.cpc}`,
      topOfSearchImpressionShare: data.previous_period.total.topOfSearchImpressionShare,
      cpa: `${data.previous_period.total.cpa}`,
      units: data.previous_period.total.units,
      sku_orders: data.previous_period.total.sku_orders,
      sku_sales: `${data.previous_period.total.sku_sales}`,
      spend_ratio: data.previous_period.total.spend_ratio || '-',
      sales_ratio: data.previous_period.total.sales_ratio || '-',
    },
    {
      key: 'change',
      period: '变动值',
      orders: data.change.orders,
      spend: data.change.spend >= 0 ? `+${data.change.spend}` : `${data.change.spend}`,
      sales: data.change.sales >= 0 ? `+${data.change.sales}` : `${data.change.sales}`,
      budget: data.change.budget >= 0 ? `+${data.change.budget}` : `${data.change.budget}`,
      acos: data.change.acos,
      cvr: data.change.cvr,
      ctr: data.change.ctr,
      impressions: data.change.impressions >= 0 ? `+${data.change.impressions}` : `${data.change.impressions}`,
      clicks: data.change.clicks >= 0 ? `+${data.change.clicks}` : `${data.change.clicks}`,
      click_ratio: data.change.click_ratio || '-',
      cpc: data.change.cpc,
      topOfSearchImpressionShare: data.change.topOfSearchImpressionShare >= 0 ? `+${data.change.topOfSearchImpressionShare}` : `${data.change.topOfSearchImpressionShare}`,
      cpa: data.change.cpa,
      units: data.change.units >= 0 ? `+${data.change.units}` : `${data.change.units}`,
      sku_orders: data.change.sku_orders >= 0 ? `+${data.change.sku_orders}` : `${data.change.sku_orders}`,
      sku_sales: data.change.sku_sales >= 0 ? `+${data.change.sku_sales}` : `${data.change.sku_sales}`,
      spend_ratio: data.change.spend_ratio || '-',
      sales_ratio: data.change.sales_ratio || '-',
    },
    {
      key: 'rate',
      period: '环比',
      orders: data.change_ratio.orders,
      spend: data.change_ratio.spend,
      sales: data.change_ratio.sales,
      budget: data.change_ratio.budget,
      acos: data.change_ratio.acos,
      cvr: data.change_ratio.cvr,
      ctr: data.change_ratio.ctr,
      impressions: data.change_ratio.impressions,
      clicks: data.change_ratio.clicks,
      click_ratio: data.change_ratio.click_ratio || '-',
      cpc: data.change_ratio.cpc,
      topOfSearchImpressionShare: data.change_ratio.topOfSearchImpressionShare,
      cpa: data.change_ratio.cpa,
      units: data.change_ratio.units,
      sku_orders: data.change_ratio.sku_orders,
      sku_sales: data.change_ratio.sku_sales,
      spend_ratio: data.change_ratio.spend_ratio || '-',
      sales_ratio: data.change_ratio.sales_ratio || '-',
    },
  ];
};

interface ComparativeAnalysisTabProps {
  campaignId?: number;
  campaignType?: string;
}

const ComparativeAnalysisTab: React.FC<ComparativeAnalysisTabProps> = ({ campaignId, campaignType }) => {
  const { productInfo } = useModel('productInfo');
  const profileId = productInfo?.profile_id;
  const currency = productInfo?.currency || '';
  const country = (productInfo?.country || '').toLowerCase();
  const tz = countryTimezoneMap[country as keyof typeof countryTimezoneMap];
  const now = tz ? dayjs().tz(tz) : dayjs();

  const [sequentialData, setSequentialData] = useState<SequentialAnalysisData | null>(null);
  const [synchronousData, setSynchronousData] = useState<SynchronousAnalysisData | null>(null);
  const [sequentialLoading, setSequentialLoading] = useState(false);
  const [synchronousLoading, setSynchronousLoading] = useState(false);

  // 添加日期范围状态
  const [sequentialDateRange, setSequentialDateRange] = useState<[Dayjs, Dayjs]>([
    now.clone().subtract(13, 'days'),
    now.clone()
  ]);
  const [synchronousDateRange, setSynchronousDateRange] = useState<[Dayjs, Dayjs]>([
    now.clone().subtract(13, 'days'),
    now.clone()
  ]);

  const getSequentialAnalysisColumns = (currency: string): TableColumnType<any>[] => [
    { title: '时间周期', width: 210, fixed: 'left' as const, dataIndex: 'period', key: 'period' },
    {
      title: createTitleWithTooltip('广告订单', 'orders'),
      width: 128, dataIndex: 'orders', key: 'orders', align: 'right'
    },
    {
      title: createTitleWithTooltip('预算', 'budget'),
      width: 118,
      dataIndex: 'budget',
      key: 'budget',
      align: 'right',
      render: (value: any, record: any) => {
        if (record.key === 'change' || record.key === 'rate') {
          return value;
        }
        return formatCurrency(value, currency);
      },
    },
    {
      title: createTitleWithTooltip('花费', 'spend'),
      width: 128,
      dataIndex: 'spend',
      key: 'spend',
      align: 'right',
      render: (value: any, record: any) => {
        if (record.key === 'change' || record.key === 'rate') {
          return value;
        }
        return formatCurrency(value, currency);
      },
    },
    {
      title: createTitleWithTooltip('销售额', 'sales'),
      width: 138,
      dataIndex: 'sales',
      key: 'sales',
      align: 'right',
      render: (value: any, record: any) => {
        if (record.key === 'change' || record.key === 'rate') {
          return value;
        }
        return formatCurrency(value, currency);
      },
    },
    {
      title: createTitleWithTooltip('ACoS', 'acos'),
      width: 118, dataIndex: 'acos', key: 'acos', align: 'right'
    },
    {
      title: createTitleWithTooltip('CVR', 'cvr'),
      width: 118, dataIndex: 'cvr', key: 'cvr', align: 'right'
    },
    {
      title: createTitleWithTooltip('CTR', 'ctr'),
      width: 118, dataIndex: 'ctr', key: 'ctr', align: 'right'
    },
    {
      title: createTitleWithTooltip('曝光量', 'impressions'),
      width: 118, dataIndex: 'impressions', key: 'impressions', align: 'right'
    },
    {
      title: createTitleWithTooltip('点击', 'clicks'),
      width: 118, dataIndex: 'clicks', key: 'clicks', align: 'right'
    },
    {
      title: createTitleWithTooltip('CPC', 'cpc'),
      width: 118,
      dataIndex: 'cpc',
      key: 'cpc',
      align: 'right',
      render: (value: any, record: any) => {
        if (record.key === 'change' || record.key === 'rate') {
          return value;
        }
        return formatCurrency(value, currency);
      },
    },
    {
      title: createTitleWithTooltip(<span>搜索结果<br />首页首位IS</span>, 'topOfSearchImpressionShare'),
      width: 148, dataIndex: 'topOfSearchImpressionShare', key: 'topOfSearchImpressionShare',
      align: 'right',
      // render: (value: any) => value === 0 ? '-' : value,
      render: () => '-',
    },
    {
      title: createTitleWithTooltip('CPA', 'cpa'),
      width: 118,
      dataIndex: 'cpa',
      key: 'cpa',
      align: 'right',
      render: (value: any, record: any) => {
        if (record.key === 'change' || record.key === 'rate') {
          return value;
        }
        return formatCurrency(value, currency);
      },
    },
    {
      title: createTitleWithTooltip('广告销量', 'units'),
      width: 128, dataIndex: 'units', key: 'units', align: 'right'
    },
    {
      title: createTitleWithTooltip(<span>直接<br />成交订单</span>, 'sku_orders'),
      width: 138, dataIndex: 'sku_orders', key: 'sku_orders', align: 'right'
    },
    {
      title: createTitleWithTooltip(<span>直接<br />成交销售额</span>, 'sku_sales'),
      width: 148,
      dataIndex: 'sku_sales',
      key: 'sku_sales',
      align: 'right',
      render: (value: any, record: any) => {
        if (record.key === 'change' || record.key === 'rate') {
          return value;
        }
        return formatCurrency(value, currency);
      },
    },
  ];

  const getSynchronousAnalysisColumns = (currency: string): TableColumnType<SynchronousAnalysisRecord>[] => [
    { title: '日期', width: 160, fixed: 'left', dataIndex: 'date', key: 'date', render: (text: string) => getWeekday(text) },
    {
      title: createTitleWithTooltip('广告订单', 'orders'),
      width: 128, dataIndex: 'orders', key: 'orders', sorter: (a: any, b: any) => genericSorter(a, b, 'orders'), align: 'right'
    },
    {
      title: createTitleWithTooltip('预算', 'budget'),
      width: 118,
      dataIndex: 'budget',
      key: 'budget',
      render: (value: any) => formatCurrency(value, currency),
      sorter: (a: any, b: any) => genericSorter(a, b, 'budget'),
      align: 'right'
    },
    {
      title: createTitleWithTooltip('花费', 'spend'),
      width: 128,
      dataIndex: 'spend',
      key: 'spend',
      render: (value: any) => formatCurrency(value, currency),
      sorter: (a: any, b: any) => genericSorter(a, b, 'spend'),
      align: 'right'
    },
    {
      title: createTitleWithTooltip('销售额', 'sales'),
      width: 138,
      dataIndex: 'sales',
      key: 'sales',
      render: (value: any) => formatCurrency(value, currency),
      sorter: (a: any, b: any) => genericSorter(a, b, 'sales'),
      align: 'right'
    },
    {
      title: createTitleWithTooltip('ACoS', 'acos'),
      width: 118, dataIndex: 'acos', key: 'acos', sorter: (a: any, b: any) => genericSorter(a, b, 'acos'), align: 'right'
    },
    {
      title: createTitleWithTooltip('CVR', 'cvr'),
      width: 118, dataIndex: 'cvr', key: 'cvr', sorter: (a: any, b: any) => genericSorter(a, b, 'cvr'), align: 'right'
    },
    {
      title: createTitleWithTooltip('CTR', 'ctr'),
      width: 118, dataIndex: 'ctr', key: 'ctr', sorter: (a: any, b: any) => genericSorter(a, b, 'ctr'), align: 'right'
    },
    {
      title: createTitleWithTooltip('曝光量', 'impressions'),
      width: 118, dataIndex: 'impressions', key: 'impressions', sorter: (a: any, b: any) => genericSorter(a, b, 'impressions'), align: 'right'
    },
    {
      title: createTitleWithTooltip('点击', 'clicks'),
      width: 118, dataIndex: 'clicks', key: 'clicks', sorter: (a: any, b: any) => genericSorter(a, b, 'clicks'), align: 'right'
    },
    {
      title: createTitleWithTooltip('CPC', 'cpc'),
      width: 118,
      dataIndex: 'cpc',
      key: 'cpc',
      render: (value: any) => formatCurrency(value, currency),
      sorter: (a: any, b: any) => genericSorter(a, b, 'cpc'),
      align: 'right'
    },
    {
      title: createTitleWithTooltip(<span>搜索结果<br />首页首位IS</span>, 'topOfSearchImpressionShare'),
      width: 148, dataIndex: 'topOfSearchImpressionShare', key: 'topOfSearchImpressionShare', sorter: (a: any, b: any) => genericSorter(a, b, 'topOfSearchImpressionShare'),
      align: 'right',
      render: (value: any, record: any) => {
        // 如果是第一层数据（有children属性的记录），直接显示 '-'
        if (record.children) {
          return '-';
        }
        // 如果是children数据，使用原有的逻辑
        return value === 0 ? '-' : value < 5 ? '< 5%' : value + ' %';
      },
    },
    {
      title: createTitleWithTooltip('CPA', 'cpa'),
      width: 118,
      dataIndex: 'cpa',
      key: 'cpa',
      render: (value: any) => formatCurrency(value, currency),
      sorter: (a: any, b: any) => genericSorter(a, b, 'cpa'),
      align: 'right'
    },
    {
      title: createTitleWithTooltip('广告销量', 'units'),
      width: 128, dataIndex: 'units', key: 'units', sorter: (a: any, b: any) => genericSorter(a, b, 'units'), align: 'right'
    },
    {
      title: createTitleWithTooltip(<span>直接<br />成交订单</span>, 'sku_orders'),
      width: 138, dataIndex: 'sku_orders', key: 'sku_orders', sorter: (a: any, b: any) => genericSorter(a, b, 'sku_orders'), align: 'right'
    },
    {
      title: createTitleWithTooltip(<span>直接<br />成交销售额</span>, 'sku_sales'),
      width: 148,
      dataIndex: 'sku_sales',
      key: 'sku_sales',
      render: (value: any) => formatCurrency(value, currency),
      sorter: (a: any, b: any) => genericSorter(a, b, 'sku_sales'),
      align: 'right'
    },
  ];

  useEffect(() => {
    if (!campaignId || !campaignType) return;
    const fetchSequentialData = async () => {
      if (!campaignId || !profileId) return;
      setSequentialLoading(true);
      try {
        const response = await getCampaignSequentialAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          profile_id: profileId,
          start_date: dayjs().subtract(14, 'days').format('YYYY-MM-DD'),
          end_date: dayjs().format('YYYY-MM-DD'),
        });
        setSequentialData(response.data);
      } finally {
        setSequentialLoading(false);
      }
    };

    const fetchSynchronousData = async () => {
      if (!campaignId || !profileId) return;
      setSynchronousLoading(true);
      try {
        const response = await getCampaignSynchronousAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          end_date: dayjs().format('YYYY-MM-DD'),
          profile_id: profileId,
          start_date: dayjs().subtract(14, 'days').format('YYYY-MM-DD'),
        });
        setSynchronousData(response.data);
      } finally {
        setSynchronousLoading(false);
      }
    };

    fetchSequentialData();
    fetchSynchronousData();
  }, [campaignId, profileId, campaignType]);

  if (!campaignId || !campaignType) return null;

  const handleSequentialDateChange = async (dates: null | (Dayjs | null)[], dateStrings: [string, string]) => {
    if (dates && dates[0] && dates[1] && campaignId && profileId) {
      setSequentialDateRange([dates[0], dates[1]]);
      setSequentialLoading(true);
      try {
        const response = await getCampaignSequentialAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          profile_id: profileId,
          start_date: dateStrings[0],
          end_date: dateStrings[1],
        });
        setSequentialData(response.data);
      } finally {
        setSequentialLoading(false);
      }
    }
  };

  const handleSynchronousDateChange = async (dates: null | (Dayjs | null)[], dateStrings: [string, string]) => {
    if (dates && dates[0] && dates[1] && campaignId && profileId) {
      setSynchronousDateRange([dates[0], dates[1]]);
      setSynchronousLoading(true);
      try {
        const response = await getCampaignSynchronousAnalysis({
          campaign_id: campaignId,
          campaign_type: campaignType,
          profile_id: profileId,
          start_date: dateStrings[0],
          end_date: dateStrings[1],
        });
        setSynchronousData(response.data);
      } finally {
        setSynchronousLoading(false);
      }
    }
  };

  return (
    <Spin spinning={sequentialLoading || synchronousLoading}>
      <div>
        <Title level={5} style={{ fontSize: 14, marginBottom: 16 }}>
          <Space>
            <span>环比分析</span>
            <PresetRangePicker
              value={sequentialDateRange}
              onChange={handleSequentialDateChange}
            />
          </Space>
        </Title>
        <Table
          columns={getSequentialAnalysisColumns(currency)}
          dataSource={convertSequentialDataToTableData(sequentialData)}
          pagination={false}
          loading={sequentialLoading}
          scroll={{ x: 'max-content' }}
        />

        <Title level={5} style={{ marginTop: 32, fontSize: 14, marginBottom: 16 }}>
          <Space>
            <span>同比分析</span>
            <PresetRangePicker
              value={synchronousDateRange}
              onChange={handleSynchronousDateChange}
            />
          </Space>
        </Title>
        <Table
          sticky
          columns={getSynchronousAnalysisColumns(currency)}
          dataSource={synchronousData?.list || []}
          className={styles.synchronousDataTable}
          pagination={false}
          loading={synchronousLoading}
          scroll={{ x: 'max-content' }}
          summary={() => {
            if (!synchronousData?.total) return null;
            return (
              <Table.Summary fixed="top">
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0}>汇总</Table.Summary.Cell>
                  <Table.Summary.Cell index={1} align="right">{synchronousData.total.orders}</Table.Summary.Cell>
                  <Table.Summary.Cell index={2} align="right">{formatCurrency(synchronousData.total.budget, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={3} align="right">{formatCurrency(synchronousData.total.spend, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={4} align="right">{formatCurrency(synchronousData.total.sales, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={5} align="right">{synchronousData.total.acos}</Table.Summary.Cell>
                  <Table.Summary.Cell index={6} align="right">{synchronousData.total.cvr}</Table.Summary.Cell>
                  <Table.Summary.Cell index={7} align="right">{synchronousData.total.ctr}</Table.Summary.Cell>
                  <Table.Summary.Cell index={8} align="right">{synchronousData.total.impressions}</Table.Summary.Cell>
                  <Table.Summary.Cell index={9} align="right">{synchronousData.total.clicks}</Table.Summary.Cell>
                  <Table.Summary.Cell index={10} align="right">{formatCurrency(synchronousData.total.cpc, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={11} align="right">-</Table.Summary.Cell>
                  <Table.Summary.Cell index={12} align="right">{formatCurrency(synchronousData.total.cpa, currency)}</Table.Summary.Cell>
                  <Table.Summary.Cell index={13} align="right">{synchronousData.total.units}</Table.Summary.Cell>
                  <Table.Summary.Cell index={14} align="right">{synchronousData.total.sku_orders}</Table.Summary.Cell>
                  <Table.Summary.Cell index={15} align="right">{formatCurrency(synchronousData.total.sku_sales, currency)}</Table.Summary.Cell>
                </Table.Summary.Row>
              </Table.Summary>
            );
          }}
          rowKey="date"
        />
      </div>
    </Spin>
  );
};

export default ComparativeAnalysisTab;