import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Typography,
  Empty,
  Tag,
  Tooltip,
  message,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import CampaignDetailModal from './CampaignDetailModal';
import { getBudgetColorTag, processNumber, processNumberOrString } from '@/utils/bus';
import { useModel } from '@umijs/max';
// import { getListingData } from '@/services/ibidder_api/operation'; // Assuming this exists
import Acos<PERSON>hart from './AcosChart';
import { getListingData } from '@/services/ibidder_api/operation';
import Vector from '@/assets/images/icon/Vector.svg';
import { getCampaignTypeColor } from '../../Common/bus';
const { Title, Text, Paragraph } = Typography;

interface CampaignBudgetPlanProps {
  campaignBudget: Strategy.DayStrategyData['campaign_budget'];
  date: string;
}

const CampaignBudgetPlan: React.FC<CampaignBudgetPlanProps> = ({ campaignBudget, date }) => {
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || ''
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [tab, setTab] = useState<"ad_place" | ''>('');
  const [selectedCampaign, setSelectedCampaign] = useState<{
    campaign_id: number;
    campaign_type: string;
    campaign_name: string;
  }>({
    campaign_id: 0,
    campaign_type: '',
    campaign_name: '',
  });
  const [listingData, setListingData] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (date && productInfo?.profile_id) {
        try {
          // In a real scenario, you would call the API
          const response = await getListingData({
            date: date,
            profile_id: productInfo.profile_id,
            asins: productInfo.asins,
            parent_asin: productInfo.asin,
          });
          if (response.code === 200) {
            setListingData(response.data);
          }
        } catch (error) {
          console.error("Failed to fetch listing data", error);
        }
      }
    };
    fetchData();
  }, [date, productInfo]);

  const handleRowClick = (record: Strategy.DayStrategyData['campaign_budget'][0], tab?: "ad_place") => {
    if (!record.campaign_id || !record.campaign_type) {
      message.error('广告活动ID或类型不存在，请联系管理员。');
      return;
    }
    setTab(tab ?? '');
    setSelectedCampaign({
      campaign_id: record.campaign_id,
      campaign_type: record.campaign_type,
      campaign_name: record.campaign_name,
    });
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    setTab('');
    setSelectedCampaign({
      campaign_id: 0,
      campaign_type: '',
      campaign_name: '',
    });
  };

  // 根据 placement 类型获取对应的竞价数据
  const getPlacementData = (record: Strategy.DayStrategyData['campaign_budget'][0], placementType: string) => {
    if (!record.placements || !Array.isArray(record.placements)) {
      return null;
    }
    const placementData = record.placements.find((placement: any) =>
      placement.type === placementType
    );
    return placementData || null;
  };

  // 获取 placement 的 bid_new 值，用于排序
  const getPlacementBidNew = (record: Strategy.DayStrategyData['campaign_budget'][0], placementType: string): number => {
    const placementData = getPlacementData(record, placementType) as any;
    if (!placementData || placementData.bid_new === '' || placementData.bid_new === null || placementData.bid_new === undefined) {
      return -1;
    }
    const bidValue = typeof placementData.bid_new === 'string' ? parseFloat(placementData.bid_new) : placementData.bid_new;
    return isNaN(bidValue) ? -1 : bidValue;
  };


  const baseColumns: ColumnsType<Strategy.DayStrategyData['campaign_budget'][0]> = [
    {
      title: '类型',
      dataIndex: 'campaign_type',
      key: 'campaign_type',
      width: 88,
      fixed: 'left',
      render: (text: string) => {
        if (text === null || text === undefined) return '-';
        return (
          <Tag color={getCampaignTypeColor(text)}>{text.toUpperCase()}</Tag>
        );
      }
    },
    {
      title: '广告活动',
      dataIndex: 'campaign_name',
      key: 'campaign_name',
      fixed: 'left',
      width: 220,
      render: (text: string, record: Strategy.DayStrategyData['campaign_budget'][0]) => (
        <a onClick={() => handleRowClick(record)} className="table-link-hover">
          {text}
        </a>
      ),
    },
    {
      title: '近期ACoS表现',
      width: 180,
      key: 'current_performance',
      render: (_: any, record: Strategy.DayStrategyData['campaign_budget'][0]) => {
        const campaignData = listingData?.[record.campaign_id];
        const acosData = campaignData?.current_list || [];
        const previousAcos = campaignData?.previous_total?.acos;
        return (
          <div>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'auto auto auto 1fr',
              gap: '4px 8px',
              alignItems: 'center'
            }}>
              <Text type="secondary">同期</Text>
              <Text type="secondary">:</Text>
              <span>{previousAcos !== undefined && previousAcos !== null ? previousAcos : '-'}</span>
              <img src={Vector} title="广告活动详情" height={12} style={{ cursor: 'pointer' }} onClick={() => handleRowClick(record)} />
            </div>
            {acosData.length > 0 ?
              <div style={{ width: 180, height: '40px' }}>
                <AcosChart key={record.campaign_id} data={acosData} />
              </div>
              : null
            }
          </div>
        );
      },
    },
    {
      title: '今日预算',
      dataIndex: 'amount',
      key: 'amount',
      width: 140,
      sorter: (a: Strategy.DayStrategyData['campaign_budget'][0], b: Strategy.DayStrategyData['campaign_budget'][0]) => (a.amount || 0) - (b.amount || 0),
      render: (amount: number, record: Strategy.DayStrategyData['campaign_budget'][0]) => {
        if (amount === null || amount === undefined) return '-';
        let t: any = '';
        let y: any = '-';
        try {
          t = `${currency}${amount.toFixed(2)}`;
        } catch (error) {
          t = amount;
        }
        if (record.benchmark_budget) {
          y = `${currency}${processNumber(record.benchmark_budget, 2)}`
        }
        return <div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span>{t}</span>
            <img src={Vector} title="广告活动详情" height={12} style={{ cursor: 'pointer' }} onClick={() => handleRowClick(record)} />
          </div>
          {
            <div style={{ whiteSpace: 'nowrap' }}><Text type="secondary" style={{ fontSize: '12px' }}>{y}（昨日）</Text></div>
          }
        </div>;
      }
    },
    {
      title: <span>搜索结果顶部<br />竞价调整</span>,
      width: 140,
      key: 'placement_top',
      sorter: (a: Strategy.DayStrategyData['campaign_budget'][0], b: Strategy.DayStrategyData['campaign_budget'][0]) =>
        getPlacementBidNew(a, 'PLACEMENT_TOP') - getPlacementBidNew(b, 'PLACEMENT_TOP'),
      render: (_: any, record: Strategy.DayStrategyData['campaign_budget'][0]) => {
        const placementData = getPlacementData(record, 'PLACEMENT_TOP');
        const placementItem = placementData as any;
        if (!placementData || placementItem.bid_old === '' || placementItem.bid_new === '') {
          return <Text>-</Text>;
        }
        const bid_old = processNumberOrString(placementItem.bid_old, '%')
        const bid_new = processNumberOrString(placementItem.bid_new, '%')
        return (
          <Tooltip
            title={
              <div>
                <div style={{ display: 'inline-grid', gridTemplateColumns: 'auto 1fr', gap: '2px 4px', alignItems: 'center' }}>
                  <span>调整后竞价：</span>
                  <span style={{ textAlign: 'right' }}>{bid_new}</span>
                  <span>调整前竞价：</span>
                  <span style={{ textAlign: 'right' }}>{bid_old}</span>
                </div>
              </div>
            }
          >
            <Text style={{ whiteSpace: 'nowrap' }}>
              <Tag color={getBudgetColorTag(bid_old, bid_new)}>{bid_new}</Tag>
              {['sp', 'sb'].includes(record.campaign_type) && <img src={Vector} title="广告活动详情" height={12} style={{ cursor: 'pointer' }} onClick={() => handleRowClick(record, 'ad_place')} />}
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>{bid_old}（昨日）</Text>
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: <span>商品页面<br />竞价调整</span>,
      width: 140,
      key: 'placement_product_page',
      sorter: (a: Strategy.DayStrategyData['campaign_budget'][0], b: Strategy.DayStrategyData['campaign_budget'][0]) =>
        getPlacementBidNew(a, 'PLACEMENT_PRODUCT_PAGE') - getPlacementBidNew(b, 'PLACEMENT_PRODUCT_PAGE'),
      render: (_: any, record: Strategy.DayStrategyData['campaign_budget'][0]) => {
        const placementData = getPlacementData(record, 'PLACEMENT_PRODUCT_PAGE');
        const placementItem = placementData as any;
        if (!placementData || placementItem.bid_old === '' || placementItem.bid_new === '') {
          return <Text>-</Text>;
        }
        const bid_old = processNumberOrString(placementItem.bid_old, '%')
        const bid_new = processNumberOrString(placementItem.bid_new, '%')
        return (
          <Tooltip
            title={
              <div>
                <div style={{ display: 'inline-grid', gridTemplateColumns: 'auto 1fr', gap: '2px 4px', alignItems: 'center' }}>
                  <span>调整后竞价：</span>
                  <span style={{ textAlign: 'right' }}>{bid_new}</span>
                  <span>调整前竞价：</span>
                  <span style={{ textAlign: 'right' }}>{bid_old}</span>
                </div>
              </div>
            }
          >
            <Text style={{ whiteSpace: 'nowrap' }}>
              <Tag color={getBudgetColorTag(bid_old, bid_new)}>{bid_new}</Tag>
              {['sp', 'sb'].includes(record.campaign_type) && <img src={Vector} title="广告活动详情" height={12} style={{ cursor: 'pointer' }} onClick={() => handleRowClick(record, 'ad_place')} />}
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>{bid_old}（昨日）</Text>
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: <span>搜索结果其余位置<br />竞价调整</span>,
      width: 165,
      key: 'placement_rest_of_search',
      sorter: (a: Strategy.DayStrategyData['campaign_budget'][0], b: Strategy.DayStrategyData['campaign_budget'][0]) =>
        getPlacementBidNew(a, 'PLACEMENT_REST_OF_SEARCH') - getPlacementBidNew(b, 'PLACEMENT_REST_OF_SEARCH'),
      render: (_: any, record: Strategy.DayStrategyData['campaign_budget'][0]) => {
        const placementData = getPlacementData(record, 'PLACEMENT_REST_OF_SEARCH');
        const placementItem = placementData as any;
        if (!placementData || placementItem.bid_old === '' || placementItem.bid_new === '') {
          return <Text>-</Text>;
        }
        const bid_old = processNumberOrString(placementItem.bid_old, '%')
        const bid_new = processNumberOrString(placementItem.bid_new, '%')
        return (
          <Tooltip
            title={
              <div>
                <div style={{ display: 'inline-grid', gridTemplateColumns: 'auto 1fr', gap: '2px 4px', alignItems: 'center' }}>
                  <span>调整后竞价：</span>
                  <span style={{ textAlign: 'right' }}>{bid_new}</span>
                  <span>调整前竞价：</span>
                  <span style={{ textAlign: 'right' }}>{bid_old}</span>
                </div>
              </div>
            }
          >
            <Text style={{ whiteSpace: 'nowrap' }}>
              <Tag color={getBudgetColorTag(bid_old, bid_new)}>{bid_new}</Tag>
              {['sp', 'sb'].includes(record.campaign_type) && <img src={Vector} title="广告活动详情" height={12} style={{ cursor: 'pointer' }} onClick={() => handleRowClick(record, 'ad_place')} />}
              <br />
              <Text type="secondary" style={{ fontSize: 12 }}>{bid_old}（昨日）</Text>
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: '调整理由',
      dataIndex: 'rationale',
      key: 'rationale',
      width: 400,
      render: (text: string) => {
        return <Tooltip title={text}>{text || '-'}</Tooltip>;
      }
    },
  ];

  return (
    <>
      <Title level={3} style={{ marginTop: 48 }}> Campaign预算规划 </Title>
      <Paragraph> *实际投放时，AI 会根据实际投放表现动态调整预算和竞价，除非需要大幅度修改，一般不建议修改 Campaign 的预算和竞价 </Paragraph>
      <Card data-test-id="day-strategy-campaign-budget" className="card">
        {campaignBudget && campaignBudget.length > 0 ? (
          <Table<Strategy.DayStrategyData['campaign_budget'][0]>
            dataSource={campaignBudget}
            columns={baseColumns}
            rowKey="campaign_id"
            pagination={false}
            scroll={{ x: 'max-content' }}
            sticky
          />
        ) : (
          <Empty description="暂无预算规划数据" />
        )}
      </Card>

      <CampaignDetailModal
        visible={isModalVisible}
        onCancel={handleModalCancel}
        campaignData={{
          campaign_id: selectedCampaign.campaign_id,
          campaign_type: selectedCampaign.campaign_type,
          campaign_name: selectedCampaign.campaign_name,
        }}
        tab={tab}
      />
    </>
  );
};

export default CampaignBudgetPlan;
