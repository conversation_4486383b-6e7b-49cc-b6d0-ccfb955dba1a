/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * proxy.ts 只在本地开发服务器生效（yarn start 系列）。执行 yarn build 时不会读取 proxy.ts
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */

export default {
  dev: {
    '/api/': {
      target: 'http://118.178.171.237:8000',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: false,
      ws: false,
      onError: (err: any, _req: any, _res: any) => {
        console.error('Proxy error:', err);
      },
    },
  },
  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    '/api/': {
      target: 'http://118.178.171.237:8000',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: false,
      ws: false,
      onError: (err: any, _req: any, _res: any) => {
        console.error('Proxy error:', err);
      },
    },
  },
  pre: {
    '/api/': {
      target: 'http://120.26.117.139',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      secure: false,
      ws: false,
      onError: (err: any, _req: any, _res: any) => {
        console.error('Proxy error:', err);
      },
    },
  },
};
