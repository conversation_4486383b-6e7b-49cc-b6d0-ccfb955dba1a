import React, { useState, useEffect } from 'react';
import { Card, Typography, Empty, message, Flex, Button } from 'antd';
import { useModel, useSearchParams } from '@umijs/max';
import Hod<PERSON><PERSON> from './HodChart';
import { editReport, getAdStrategy, approveReport, getRoleAgentDetail } from '@/services/ibidder_api/operation';
import DayStrategyCard from '../../Common/DayStrategyCard';
import DayStrategyExpectedResults from '../../Common/DayStrategyExpectedResults';
import AdsTrendCard from '../../Common/AdsTrendCard';

import DateNavigator from './DateNavigator';
import RevisionHistory from './RevisionHistory';
import { getCountryTimezone } from '@/utils/bus';
import EmptyState from '@/pages/ListingDetail/Common/EmptyState';
import ReportFailureState from '@/pages/ListingDetail/Common/ReportFailureState';
import GeneratingState from '@/pages/ListingDetail/Common/GeneratingState';
import ConfirmationSection from './ConfirmationSection';
import FeedbackButton from './FeedbackButton';
import WeeklyProgressAnalysis from './WeeklyProgressAnalysis';
import CampaignBudgetPlan from './CampaignBudgetPlan';

import { getDayOfWeek } from '@/utils/bus';
import { DayStrategyModalData } from '@/models/globalModals';
import { dayStrategyConfig, normalizeDataRecursively } from '@/utils/dataTransformer';
import { Loading } from '@/components';
import { EditOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface DayStrategyContentProps extends DayStrategyModalData {
  onTitleChange?: (title: string) => void;
  onCancel?: (refresh: boolean) => void;
  onSuccess: () => void;
  showControlBtn?: boolean;
  /** 审核区域是否显示 看板上不显示 */
  showReviewArea?: boolean;
}

// 广告日策略组件
const DayStrategyContent: React.FC<DayStrategyContentProps> = (props) => {
  const {
    job_id,
    target_job_id,
    onTitleChange,
    onCancel = () => { },
    onSuccess,
    date,
    showControlBtn,
    showReviewArea = true
  } = props

  // 全局状态管理
  const { updateAlertFn } = useModel('updateAlert');
  const [searchParams] = useSearchParams();
  const parent_asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { productInfo } = useModel('productInfo');
  const country = productInfo?.country || '';
  const currency = productInfo?.currency || ''
  const { openDayStrategyCatModal, openDayStrategyModal } = useModel('globalModals');
  ;
  const [ajaxData, setAjaxData] = useState<AjaxData.DayStrategyData | null>(null);
  const [aiFeedbackContent, setAiFeedbackContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [isFirstDate, setIsFirstDate] = useState(false);

  const fetchStrategyData = async () => {
    // 通过 getAdStrategy 接口获取报告数据
    if (parent_asin && profile_id && date) {
      try {
        setLoading(true);
        // 初始加载时默认认为是第一个日期
        setIsFirstDate(true);
        const res = await getAdStrategy<AjaxData.DayStrategyData>({
          date: date,
          job_id: job_id || 'ads_strategy_day',
          asin: parent_asin,
          profile_id,
        });
        setLoading(false);

        if (res.code === 200) {
          setAjaxData(res.data);

          if (res.data?.result?.ads_strategy_day) {
            // 设置弹框标题
            const rawStrategyData = res.data.result.ads_strategy_day;

            const normalizedData = normalizeDataRecursively<Strategy.DayStrategyData>(rawStrategyData, dayStrategyConfig);

            if (onTitleChange && normalizedData?.date) {
              const dayOfWeek = getDayOfWeek(normalizedData.date);
              const title = `日广告投放策略（${normalizedData.date} ${dayOfWeek}）`;
              onTitleChange(title);
            }
          }
        }
      } catch (error) {
        console.error('获取策略数据失败:', error);
        message.error('获取数据失败，请重试');
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchStrategyData();
  }, [parent_asin, profile_id, job_id, target_job_id, country, date]); // Added country to dependencies

  // 统一的 ajaxData 异常判断，如果没有数据就直接返回
  if (!ajaxData) {
    return null
  }

  // 从 ajaxData 中派生的计算属性
  const current_time = ajaxData.current_time;
  const dayStrategyData = ajaxData.result && ajaxData.result.ads_strategy_day
    ? normalizeDataRecursively<Strategy.DayStrategyData>(ajaxData.result.ads_strategy_day, dayStrategyConfig)
    : null;

  const _real_ads_result = ajaxData.result && ajaxData.result.real_ads_result;
  const daypartAdsAdjustments = ajaxData.daypart_ads_adjustments;
  // 全局modal控制

  // 检查 daypartAdsAdjustments 是否有有效数据
  const hasValidAdjustmentData = (data: Strategy.DaypartAdsAdjustments | null): boolean => {
    if (!data) return false;

    // 检查 amount 是否有有效数据
    const hasValidAmount = data.amount &&
      data.amount.new !== null &&
      data.amount.old !== null &&
      (data.amount.new !== data.amount.old);

    // 检查 set_campaign_budget 是否有有效数据
    const hasValidCampaignBudget = !!(data.set_campaign_budget?.set_campaign_budget &&
      data.set_campaign_budget.set_campaign_budget.trim() !== '');

    // 检查 set_placement_bidding 是否有有效数据
    const hasValidPlacementBidding = !!(data.set_placement_bidding?.set_placement_bidding &&
      data.set_placement_bidding.set_placement_bidding.trim() !== '');

    return !!(hasValidAmount || hasValidCampaignBudget || hasValidPlacementBidding);
  };

  /** 日期切换 */
  const handleStrategyDataUpdate = (data: AjaxData.DayStrategyData | null) => {
    setAjaxData(data);

    if (data) {
      // 设置弹框标题
      const normalizedData = data.result.ads_strategy_day
        ? normalizeDataRecursively<Strategy.DayStrategyData>(data.result.ads_strategy_day, dayStrategyConfig)
        : null;

      if (onTitleChange && normalizedData?.date) {
        const { date } = normalizedData;
        const dayOfWeek = getDayOfWeek(date);
        const title = `日广告投放策略（${date} ${dayOfWeek}）`;
        onTitleChange(title);
      }
    }
  };

  // DateNavigator的日期变更回调
  const handleDateChange = async (apiDate: string, isFirst: boolean = false) => {
    setIsFirstDate(isFirst);
    setLoading(true);
    try {
      const res: any = await getAdStrategy({
        date: apiDate,
        asin: parent_asin,
        profile_id,
        job_id: 'ads_strategy_day',
      });
      setLoading(false);

      if (res.code === 200 && res.data) {
        handleStrategyDataUpdate(res.data);
      } else {
        handleStrategyDataUpdate(null);
      }
    } catch (error) {
      setLoading(false);
      handleStrategyDataUpdate(null);
    }
  };

  // DateNavigator的版本变更回调
  const handleVersionChange = async (targetEsId: string) => {
    setLoading(true);
    try {
      const res: any = await getRoleAgentDetail({ es_id: targetEsId });
      setLoading(false);

      if (res.code === 200) {
        handleStrategyDataUpdate(res.data);
      } else {
        // message.error('获取版本数据失败');
      }
    } catch (error) {
      setLoading(false);
      // message.error('获取版本数据失败，请重试');
    }
  };

  // 修改方法
  const handleModify = async () => {
    if (!current_time || !parent_asin || !job_id || !profile_id) {
      console.error("缺少必要的参数，无法提交");
      return;
    }

    // 如果 report_status 是 false，需要先审核
    if (!ajaxData.report_status) {
      try {
        // 先调用审核接口
        await approveReport({
          profile_id: profile_id,
          es_id: ajaxData.es_id,
        });
      } catch (error) {
        console.error('审核失败:', error);
      }
    }

    // 如果没有AI反馈内容
    if (!aiFeedbackContent.trim()) {
      return;
    }

    // 如果有AI反馈内容，将其保存到dayStrategyData中
    const updatedDayStrategyData = { ...dayStrategyData };
    if (aiFeedbackContent.trim()) {
      updatedDayStrategyData.ai_feedbackContent = aiFeedbackContent.trim();
    }

    const payload = {
      es_id: ajaxData.es_id,
      current_time: current_time,
      parent_asin: parent_asin,
      asins: ajaxData.asins,
      role: ajaxData.role,
      job_id: target_job_id || job_id,
      profile_id: profile_id,
      country: country,
      data: {
        ads_strategy_day: updatedDayStrategyData,
      },
    };
    try {
      const res = await editReport(payload);
      if (res.code === 200) {
        message.success('修改已成功提交');
        if (onCancel) onCancel(true); // 只有成功时才关闭弹框
        // 更新全局状态
        updateAlertFn(true);
        onSuccess();
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error('修改失败:', error);
    }
  };

  // 审核方法
  const handleApprove = async () => {
    approveReport({
      profile_id: profile_id,
      es_id: ajaxData.es_id,
    }).then((res) => {
      if (res.code === 200) {
        // 更新全局状态
        updateAlertFn(true);
        onSuccess();
        message.success('审核已成功提交');
        if (onCancel) onCancel(true);
      } else {
        message.error(res?.message || '审核失败，请重试');
      }
    }).catch((error) => {
      console.error('审核失败:', error);
    });
  };

  let contentCards;

  if (ajaxData.is_generating === true) {
    contentCards = (
      <GeneratingState text="日广告投放策略正在制定中，请稍候 ~" />
    );
  } else if (ajaxData.success === false) {
    contentCards = (
      <ReportFailureState
        rerunParams={{
          asins: ajaxData.asins || [],
          country: country,
          current_time: ajaxData.current_time || '',
          es_id: ajaxData.es_id || '',
          job_id: ajaxData.job_id || job_id || '',
          parent_asin: parent_asin,
          profile_id: profile_id,
          role: ajaxData.role || '',
        }}
        onRerunSuccess={() => {
          // 重新生成成功后，重新获取当前报告数据
          fetchStrategyData();
        }}
        showRerunFeature={isFirstDate}
      />
    );
  } else if (!ajaxData || !dayStrategyData) {
    contentCards = (
      <EmptyState
        imageWidth={180}
        text="未查到对应日期的报告"
      />
    );
  } else {
    contentCards = (
      <>
        {/* 最新调整 Card */}
        {daypartAdsAdjustments && (
          <Card
            data-test-id="day-strategy"
            className="card"
            style={{ marginTop: 24 }}
            title={
              <Flex align="center" justify="space-between">
                <Flex align="center" gap={8}>
                  <span>最新调整</span>
                  <div style={{
                    width: '6px',
                    height: '6px',
                    borderRadius: '50%',
                    backgroundColor: '#ff4d4f'
                  }} />
                </Flex>
                <div>
                  {
                    daypartAdsAdjustments?.daypart_report !== null &&
                    <Button
                      type="link"
                      style={{ padding: 0, height: 'auto' }}
                      onClick={() => {
                        if (daypartAdsAdjustments?.daypart_report) {
                          const title = `广告优化调整${daypartAdsAdjustments.daypart_report.current_time ? ` (${daypartAdsAdjustments.daypart_report.current_time} ${getDayOfWeek(daypartAdsAdjustments.daypart_report.current_time)})` : ''}`;
                          openDayStrategyCatModal({
                            job_id: daypartAdsAdjustments.daypart_report.job_id,
                            current_time: daypartAdsAdjustments.daypart_report.current_time,
                            target_job_id: target_job_id,
                          }, title);
                        }
                      }}
                    >
                      查看详情
                    </Button>
                  }
                </div>
              </Flex>
            }
          >
            {daypartAdsAdjustments && (
              <div>
                <Text style={{ color: '#1890ff', fontSize: '14px' }}>
                  广告优化调整
                  {
                    daypartAdsAdjustments.daypart_report?.current_time ?
                      ` (${daypartAdsAdjustments.daypart_report?.current_time?.split(' ')[0]} ${daypartAdsAdjustments.daypart_report?.current_time?.split(' ')[1]} ${country})`
                      :
                      ` (${dayStrategyData.date} ${country})`
                  }
                </Text>
                <ul className="goalList" style={{ marginTop: '12px' }}>
                  {hasValidAdjustmentData(daypartAdsAdjustments) ? (
                    <>
                      {daypartAdsAdjustments.amount && (daypartAdsAdjustments.amount.new || daypartAdsAdjustments.amount.old) && (
                        <li className="goalItem">
                          <div className="goalDot"></div>
                          <div style={{ flex: 1 }}>
                            {
                              daypartAdsAdjustments.amount.old === undefined || daypartAdsAdjustments.amount.old === null ?
                                `今日预算：${currency}${daypartAdsAdjustments.amount.new}`
                                :
                                `预算调整：${currency}${daypartAdsAdjustments.amount.old ?? '-'} → ${currency}${daypartAdsAdjustments.amount.new ?? '-'}`
                            }
                          </div>
                        </li>
                      )}
                      {daypartAdsAdjustments.set_campaign_budget?.set_campaign_budget && (
                        <li className="goalItem">
                          <div className="goalDot"></div>
                          <div style={{ flex: 1 }}>
                            Campaign预算：{daypartAdsAdjustments.set_campaign_budget.set_campaign_budget}
                          </div>
                        </li>
                      )}
                      {daypartAdsAdjustments.set_placement_bidding?.set_placement_bidding && (
                        <li className="goalItem">
                          <div className="goalDot"></div>
                          <div style={{ flex: 1 }}>
                            广告位竞价：{daypartAdsAdjustments.set_placement_bidding.set_placement_bidding}
                          </div>
                        </li>
                      )}
                    </>
                  ) : (
                    <li className="goalItem">
                      <div className="goalDot"></div>
                      <div style={{ flex: 1 }}>
                        该时间点暂无任何调整
                      </div>
                    </li>
                  )}

                </ul>
              </div>
            )}
          </Card>
        )}

        <AdsTrendCard type="day" style={{ marginTop: 24 }} />

        <WeeklyProgressAnalysis
          data={dayStrategyData?.weekly_progress_analysis}
          time={ajaxData.current_time}
        />

        <Title level={3} style={{ marginTop: 48 }}>投放策略和预算</Title>
        <DayStrategyCard
          dayStrategyData={dayStrategyData}
        />

        <Title level={3} style={{ marginTop: 48 }}>预期结果</Title>
        <DayStrategyExpectedResults expected_results={dayStrategyData.expected_results} real_ads_result={_real_ads_result} />

        <CampaignBudgetPlan
          campaignBudget={dayStrategyData.campaign_budget}
          date={dayStrategyData.date}
        />

        <Title level={3} style={{ marginTop: 48 }}>分时竞价策略</Title>
        <Card data-test-id="day-strategy-hod-bid-adjustment" className="card" style={{ marginTop: "16px" }}>
          {dayStrategyData?.hod_bid_adjustment.adjustments.length > 0 ? (
            <>
              <HodChart
                hodBidding={dayStrategyData?.hod_bid_adjustment.adjustments}
                multipleXAxis
              />
            </>
          ) : (
            <Empty description="暂无分时竞价数据" />
          )}
          {
            dayStrategyData?.hod_bid_adjustment.rationale && (
              <div style={{ marginTop: '10px', padding: '0 10px' }}>
                <Title level={5}>调整理由：</Title>
                <Text>{dayStrategyData?.hod_bid_adjustment.rationale}</Text>
              </div>
            )
          }
        </Card>

        {/* 修订记录 */}
        <RevisionHistory
          revision_history={dayStrategyData?.revision_history}
        />
      </>
    );
  }

  const viewDayStrategy = () => {
    const title = `日广告投放策略（${ajaxData.target_week || ''} ${getDayOfWeek(ajaxData.target_week)}）`;
    const modalData: DayStrategyModalData = {
      current_time: ajaxData.current_time,
      job_id: ajaxData.job_id || '',
      date: ajaxData.target_week || getCountryTimezone(country, 'YYYY-MM-DD'),
    };
    openDayStrategyModal(modalData, title);
  };

  return (
    <div className='report-modal-div'>
      <div className='report-modal-content'>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 24, marginBottom: 24, gap: 16 }}>
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <DateNavigator
              initialDate={ajaxData.target_week || getCountryTimezone(country, 'YYYY-MM-DD')}
              current_time={ajaxData.current_time}
              job_id={'ads_strategy_day'}
              dateType='single'
              es_id={ajaxData.es_id}
              onDateChange={handleDateChange}
              onVersionChange={handleVersionChange}
            />
          </div>
          {ajaxData.can_edit && showControlBtn && <Button onClick={viewDayStrategy}><EditOutlined />修改策略</Button>}
          <FeedbackButton
            feedbackParams={{
              parent_asin: parent_asin,
              profile_id: profile_id,
              job_id: job_id || '',
              es_id: ajaxData.es_id,
              current_time: current_time || '',
            }}
          />
        </div>

        {
          loading ?
            <Loading />
            :
            contentCards
        }
      </div>

      {showReviewArea && ajaxData.can_edit && (
        <ConfirmationSection
          report_status={ajaxData.report_status}
          value={aiFeedbackContent}
          onChange={(e) => setAiFeedbackContent(e.target.value)}
          onApprove={handleApprove}
          onModify={handleModify}
          minRows={5}
          maxRows={8}
          placeholder='请输入修改意见，每行一条。AI将根据您的意见进行修订。
例如：
● 将总体投放策略修改为激进
● 今天的销售目标是 1000 美元，基于这个目标制定策略
● AI 会根据实际投放表现动态调整预算和竞价，除非需要大幅度修改，一般不建议修改 Campaign 的预算和竞价'
        />
      )}
    </div>
  );
};

export default DayStrategyContent;
