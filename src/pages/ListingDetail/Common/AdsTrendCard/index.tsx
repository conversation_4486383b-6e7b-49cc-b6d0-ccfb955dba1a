import React, { useState } from 'react';
import { Card, Flex, Segmented } from 'antd';
import AdsTrendComponent from './AdsTrendComponent';
import AdsTimeSlotComponent from './AdsTimeSlotComponent';

type AdsTrendCardProps = {
  style?: React.CSSProperties;
  type: 'day' | 'week' | 'month';
};

const AdsTrendCard: React.FC<AdsTrendCardProps> = (props) => {
  const [position, setPosition] = useState<string>('trend');

  return (
    <Card data-testid="ads-trend-card" className="card" style={{ ...props.style }}>
      <Flex justify="space-between" align="center" style={{ marginBottom: 24 }}>
        <Segmented
          options={[{ label: '广告业绩走势', value: 'trend' }, { label: '广告出单时段', value: '24H' }]}
          onChange={(value) => setPosition(value)}
          value={position}
        />
      </Flex>

      {position === 'trend' ? (
        <AdsTrendComponent type={props.type} />
      ) : (
        <AdsTimeSlotComponent type={props.type} />
      )}
    </Card>
  );
};

export default AdsTrendCard;
