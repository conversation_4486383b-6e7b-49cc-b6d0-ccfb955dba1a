import React from 'react';
import { Row, Col, Card, Typography, Statistic, Tag, Space, message, Flex, Table, Button } from 'antd';
import { useModel, useSearchParams } from '@umijs/max';
import { RiseOutlined, FallOutlined, EditOutlined } from '@ant-design/icons';
import { getBaseReport, editReport, approveReport, getAdStrategy, getRoleAgentDetail } from '@/services/ibidder_api/operation';
import { marketTrendsConfig, normalizeDataRecursively, weekAnalysisConfig } from '@/utils/dataTransformer';
import { useState, useEffect } from 'react';

import WeekAnalysisCard from '../../Common/WeekAnalysisCard';

import MonthlyTrends from '../../Common/MonthlyTrends';
import AdsTrendCard from '../../Common/AdsTrendCard';
import RevisionHistory from './RevisionHistory';
import ConfirmationSection from './ConfirmationSection';
import FeedbackButton from './FeedbackButton';
import DateNavigator from './DateNavigator';
import EmptyState from '../../Common/EmptyState';
import ReportFailureState from '../../Common/ReportFailureState';
import GeneratingState from '../../Common/GeneratingState';
import { WeekMonthReportModalData } from '@/models/globalModals';
import { Loading } from '@/components';

const { Title, Text } = Typography;

interface WeekAnalysisContentProps extends WeekMonthReportModalData {
  type: 'week' | 'month';
  onTitleChange?: (title: string) => void;
  onCancel?: (refresh: boolean) => void;
  // 确定成功后执行的回调函数
  onSuccess: () => void;
  showControlBtn?: boolean;
  /** 审核区域是否显示 看板上不显示 */
  showReviewArea?: boolean;
}

// 市场分析报告组件
const MonthAnalysisContent: React.FC<WeekAnalysisContentProps> = (props) => {
  const {
    job_id,
    onTitleChange,
    onCancel = () => { },
    onSuccess,
    showControlBtn,
    showReviewArea = true
  } = props
  // 全局状态管理
  const { updateAlertFn } = useModel('updateAlert');
  const { openMonthReportModal } = useModel('globalModals');
  const [searchParams] = useSearchParams();
  const [ajaxData, setAjaxData] = useState<AjaxData.MonthAnalysisData | null>(null)
  const parent_asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { productInfo } = useModel('productInfo');
  const country = productInfo?.country || '';
  const [loading, setLoading] = useState(false)
  const [aiFeedbackContent, setAiFeedbackContent] = useState<string>('');
  const [isFirstDate, setIsFirstDate] = useState(false);

  // 获取市场分析数据
  const fetchWeekAnalysisData = async () => {
    if (parent_asin && profile_id && job_id && props.current_time) {
      try {
        setLoading(true);
        // 初始加载时默认认为是第一个日期
        setIsFirstDate(true);
        const res: any = await getBaseReport({
          job_id,
          asin: parent_asin,
          profile_id,
          current_time: props.current_time,
          target_job_id: props.target_job_id,
        });
        setLoading(false);

        if (res.data) {
          // 根据 type 判断获取哪种类型的数据
          if (props.type === 'week') {
            setAjaxData(res.data);
            if (res.data.result.market_report_week) {
              // 设置弹框标题
              const normalizedData = normalizeDataRecursively<Strategy.WeekAnalysisData>(
                res.data.result.market_report_week,
                weekAnalysisConfig
              );

              if (onTitleChange && normalizedData) {
                const { start_date, end_date } = normalizedData;
                const title = `周市场分析报告（${start_date} ~ ${end_date}）`;
                onTitleChange(title);
              }
            }

            return;
          } else if (props.type === 'month') {
            setAjaxData(res.data);
            if (res.data.result.market_report_month) {

              // 设置弹框标题
              const normalizedData = normalizeDataRecursively<Strategy.WeekAnalysisData>(
                res.data.result.market_report_month,
                weekAnalysisConfig
              );

              if (onTitleChange && normalizedData) {
                const { start_date, end_date } = normalizedData;
                const title = `月市场分析报告（${start_date} ~ ${end_date}）`;
                onTitleChange(title);
              }
            }

            return;
          }
        }
      } catch (error) {
        console.error('获取市场分析数据失败:', error);
        message.error('获取数据失败，请重试');
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchWeekAnalysisData();
  }, [parent_asin, profile_id, job_id, props.current_time]);

  // 统一的 ajaxData 异常判断，如果没有数据就直接返回
  if (!ajaxData) {
    return null
  }

  // 从 ajaxData 中派生的计算属性
  const current_time = ajaxData.current_time;
  const weekAnalysisData = ajaxData.result ? (
    props.type === 'week' && ajaxData.result.market_report_week
      ? normalizeDataRecursively<Strategy.WeekAnalysisData>(ajaxData.result.market_report_week, weekAnalysisConfig)
      : props.type === 'month' && ajaxData.result.market_report_month
        ? normalizeDataRecursively<Strategy.WeekAnalysisData>(ajaxData.result.market_report_month, weekAnalysisConfig)
        : null
  ) : null;
  const normalizedMonthlyTrends = ajaxData.result && ajaxData.result.market_trends ? normalizeDataRecursively<Strategy.MarketTrends>(ajaxData.result.market_trends, marketTrendsConfig) : { monthly_trends: [] };
  const market_trends = normalizedMonthlyTrends;
  const google_searchs = ajaxData.google_searchs;

  // 审核方法
  const handleApprove = async () => {
    approveReport({
      profile_id: profile_id,
      es_id: ajaxData.es_id,
    }).then((res) => {
      if (res.code === 200) {
        // 更新全局状态
        updateAlertFn(true);
        onSuccess();
        // message.success('审核已成功提交');
        if (onCancel) onCancel(true);
      } else {
        console.error(res?.message || '审核失败，请重试');
      }
    }).catch((error) => {
      console.error(error);
    })
  };

  // 修改方法
  const handleModify = async () => {
    if (!current_time || !parent_asin || !job_id || !profile_id) {
      console.error("缺少必要的参数，无法提交");
      return;
    }

    // 如果 report_status 是 false，需要先审核
    if (!ajaxData.report_status) {
      try {
        // 先调用审核接口
        await approveReport({
          profile_id: profile_id,
          es_id: ajaxData.es_id,
        });
      } catch (error) {
        console.error(error);
      }
    }

    // 如果没有AI反馈内容
    if (!aiFeedbackContent.trim()) {
      return;
    }

    // 创建一个深拷贝的数据对象
    const updatedWeekAnalysisData = weekAnalysisData ? JSON.parse(JSON.stringify(weekAnalysisData)) : {};

    // 如果有AI反馈内容，保存到数据对象中
    if (aiFeedbackContent.trim()) {
      updatedWeekAnalysisData.ai_feedbackContent = aiFeedbackContent.trim();
    }

    const payload = {
      es_id: ajaxData.es_id,
      current_time: current_time,
      parent_asin: parent_asin,
      asins: ajaxData.asins,
      role: ajaxData.role,
      job_id: job_id,
      profile_id: profile_id,
      country: country,
      data: {
        [job_id]: updatedWeekAnalysisData,
      },
    };
    try {
      const res = await editReport(payload);
      if (res && res.code === 200) {
        // message.success('修改已成功提交');
        if (onCancel) onCancel(true); // 只有成功时才关闭弹框
        // 更新全局状态
        updateAlertFn(true);
        onSuccess();
      } else {
        console.error(res?.message || '修改提交失败');
      }
    } catch (error) {
      console.error(error);
    }
  };



  // 处理导航器数据更新的回调函数
  const handleStrategyDataUpdate = (data: AjaxData.MonthAnalysisData | null) => {
    if (data) {
      setAjaxData(data);

      // 设置弹框标题
      let normalizedData = null;
      if (props.type === 'week') {
        if (data.result.market_report_week) {

          normalizedData = normalizeDataRecursively<Strategy.WeekAnalysisData>(
            data.result.market_report_week,
            weekAnalysisConfig
          );

          if (onTitleChange && normalizedData) {
            const { start_date, end_date } = normalizedData;
            const title = `周市场分析报告（${start_date} ~ ${end_date}）`;
            onTitleChange(title);
          }
        }
      } else if (props.type === 'month') {
        if (data.result.market_report_month) {

          normalizedData = normalizeDataRecursively<Strategy.WeekAnalysisData>(
            data.result.market_report_month,
            weekAnalysisConfig
          );

          if (onTitleChange && normalizedData) {
            const { start_date, end_date } = normalizedData;
            const title = `月市场分析报告（${start_date} ~ ${end_date}）`;
            onTitleChange(title);
          }
        }
      }
    } else {
      setAjaxData(null);
    }
  };

  // DateNavigator的日期变更回调
  const handleDateChange = async (apiDate: string, isFirst: boolean = false) => {
    setIsFirstDate(isFirst);
    setLoading(true);
    try {
      const res: any = await getAdStrategy({
        date: apiDate,
        asin: parent_asin,
        profile_id,
        job_id: props.type === 'week' ? 'market_report_week' : 'market_report_month',
      });
      setLoading(false);

      if (res.code === 200 && res.data) {
        handleStrategyDataUpdate(res.data);
      } else {
        handleStrategyDataUpdate(null);
      }
    } catch (error) {
      setLoading(false);
      handleStrategyDataUpdate(null);
    }
  };

  // DateNavigator的版本变更回调
  const handleVersionChange = async (targetEsId: string) => {
    setLoading(true);
    try {
      const res: any = await getRoleAgentDetail({ es_id: targetEsId });
      setLoading(false);

      if (res.code === 200) {
        handleStrategyDataUpdate(res.data);
      } else {
        // message.error('获取版本数据失败');
      }
    } catch (error) {
      setLoading(false);
      // message.error('获取版本数据失败，请重试');
    }
  };

  let contentCards;

  if (ajaxData.is_generating === true) {
    contentCards = (
      <GeneratingState text={`${props.type === 'week' ? '周' : '月'}市场分析报告正在制定中，请稍候 ~`} />
    );
  } else if (ajaxData.success === false) {
    contentCards = (
      <ReportFailureState
        rerunParams={{
          asins: ajaxData.asins || [],
          country: country,
          current_time: ajaxData.current_time || '',
          es_id: ajaxData.es_id || '',
          job_id: ajaxData.job_id || job_id || '',
          parent_asin: parent_asin,
          profile_id: profile_id,
          role: ajaxData.role || '',
        }}
        onRerunSuccess={() => {
          // 重新生成成功后，重新获取当前报告数据
          fetchWeekAnalysisData();
        }}
        showRerunFeature={isFirstDate}
      />
    );
  } else if (!weekAnalysisData || !weekAnalysisData.ads_suggestion) {
    contentCards = (
      <EmptyState
        imageWidth={180}
        text="未查到对应日期的报告"
      />
    );
  } else {
    contentCards = (
      <>
        <AdsTrendCard type={props.type} />

        {/* 市场概述 和 月度趋势 */}
        <Row gutter={16} style={{ marginTop: 16 }} className='card-row'>
          <Col span={12}>
            <Card data-test-id="week-analysis-overview" className="card">
              <Flex justify="space-between" align="center" style={{ marginBottom: "0.8em" }}>
                <Title level={4} style={{ margin: 0 }}>市场概述</Title>
              </Flex>
              <Text style={{ fontSize: "16px" }}>{weekAnalysisData.forecast?.overview}</Text>
            </Card>
          </Col>
          {
            market_trends && market_trends.monthly_trends.length > 0 &&
            <Col span={12}>
              <Card data-test-id="week-analysis-trends" className="card">
                <Title level={4} style={{ margin: 0 }}>市场趋势分析 <span style={{ fontSize: 14, color: '#9d9d9d', fontWeight: 'normal' }}>反映市场整体趋势，并非本商品销售趋势</span> </Title>
                <div style={{ height: '160px' }}>
                  <MonthlyTrends chartData={market_trends.monthly_trends} />
                </div>
              </Card>
            </Col>
          }
        </Row>

        {/* 市场前瞻 趋势前瞻 & 关键指标预测 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} style={{ margin: 0 }}>市场前瞻</Title>
        </Flex>
        <Row gutter={16}>
          <Col span={12}>
            <Card data-test-id="week-analysis-forecast" className="card" style={{ height: "100%" }}>
              <Flex justify="space-between" align="center">
                <Title level={4} style={{ margin: 0 }}>趋势前瞻</Title>
              </Flex>
              <ul className="goalList" style={{ marginTop: "16px" }}>
                {weekAnalysisData.forecast?.market_preview.map((item, index) => (
                  <li key={index} className="goalItem">
                    <div className="goalDot"></div>
                    <Text>{item}</Text>
                  </li>
                ))}
              </ul>

            </Card>
          </Col>
          <Col span={12}>
            <Card data-test-id="week-analysis-metrics" className='card' style={{ height: "100%" }}>
              <Flex justify="space-between" align="center">
                <Title level={4} style={{ margin: 0 }}>关键指标预测</Title>
              </Flex>
              <Row style={{ marginTop: 24, alignContent: "center" }}>
                <Col span={6} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Text>流量</Text>
                  <Statistic
                    title=""
                    value=" "
                    prefix={weekAnalysisData.forecast?.metrics_forecast.traffic === 'up' ?
                      <RiseOutlined style={{ fontSize: "48px" }} /> :
                      weekAnalysisData.forecast?.metrics_forecast.traffic === 'down' ?
                        <FallOutlined style={{ fontSize: "48px" }} /> :
                        <RiseOutlined rotate={35} style={{ fontSize: "48px" }} />}
                    valueStyle={{
                      color: weekAnalysisData.forecast?.metrics_forecast.traffic === 'up' ?
                        '#f5222d' : weekAnalysisData.forecast?.metrics_forecast.traffic === 'down' ?
                          '#52c41a' : '#1890ff'
                    }}
                  />
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>{weekAnalysisData.forecast?.metrics_forecast.traffic === 'up' ?
                    '上升' : weekAnalysisData.forecast?.metrics_forecast.traffic === 'down' ?
                      '下降' : '稳定'}</Text>
                </Col>
                <Col span={6} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Text>销售</Text>
                  <Statistic
                    title=""
                    value=" "
                    prefix={weekAnalysisData.forecast?.metrics_forecast.sales === 'up' ?
                      <RiseOutlined style={{ fontSize: "48px" }} /> :
                      weekAnalysisData.forecast?.metrics_forecast.sales === 'down' ?
                        <FallOutlined style={{ fontSize: "48px" }} /> :
                        <RiseOutlined rotate={35} style={{ fontSize: "48px" }} />}
                    valueStyle={{
                      color: weekAnalysisData.forecast?.metrics_forecast.sales === 'up' ?
                        '#f5222d' : weekAnalysisData.forecast?.metrics_forecast.sales === 'down' ?
                          '#52c41a' : '#1890ff'
                    }}
                  />
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>{weekAnalysisData.forecast?.metrics_forecast.sales === 'up' ?
                    '上升' : weekAnalysisData.forecast?.metrics_forecast.sales === 'down' ?
                      '下降' : '稳定'}</Text>
                </Col>
                <Col span={6} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Text>广告支出</Text>
                  <Statistic
                    title=""
                    value=" "
                    prefix={weekAnalysisData.forecast?.metrics_forecast.spend === 'up' ?
                      <RiseOutlined style={{ fontSize: "48px" }} /> :
                      weekAnalysisData.forecast?.metrics_forecast.spend === 'down' ?
                        <FallOutlined style={{ fontSize: "48px" }} /> :
                        <RiseOutlined rotate={35} style={{ fontSize: "48px" }} />}
                    valueStyle={{
                      color: weekAnalysisData.forecast?.metrics_forecast.spend === 'up' ?
                        '#f5222d' : weekAnalysisData.forecast?.metrics_forecast.spend === 'down' ?
                          '#52c41a' : '#1890ff'
                    }}
                  />
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>{weekAnalysisData.forecast?.metrics_forecast.spend === 'up' ?
                    '上升' : weekAnalysisData.forecast?.metrics_forecast.spend === 'down' ?
                      '下降' : '稳定'}</Text>
                </Col>
                <Col span={6} style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <Text>ACoS</Text>
                  <Statistic
                    title=""
                    value=" "
                    prefix={weekAnalysisData.forecast?.metrics_forecast.acos === 'up' ?
                      <RiseOutlined style={{ fontSize: "48px" }} /> :
                      weekAnalysisData.forecast?.metrics_forecast.acos === 'down' ?
                        <FallOutlined style={{ fontSize: "48px" }} /> :
                        <RiseOutlined rotate={35} style={{ fontSize: "48px" }} />}
                    valueStyle={{
                      color: weekAnalysisData.forecast?.metrics_forecast.acos === 'up' ?
                        '#f5222d' : weekAnalysisData.forecast?.metrics_forecast.acos === 'down' ?
                          '#52c41a' : '#1890ff'
                    }}
                  />
                  <Text style={{ fontSize: "16px", fontWeight: "bold" }}>{weekAnalysisData.forecast?.metrics_forecast.acos === 'up' ?
                    '上升' : weekAnalysisData.forecast?.metrics_forecast.acos === 'down' ?
                      '下降' : '稳定'}</Text>
                </Col>
              </Row>

            </Card>
          </Col>
        </Row>

        {/* SWOT分析 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} style={{ margin: 0 }}>SWOT 分析</Title>
        </Flex>
        <div>
          <Row gutter={16} style={{ display: 'flex' }}>
            <Col span={12} style={{ display: 'flex' }}>
              <Card data-test-id="week-analysis-swot-strengths" style={{ backgroundColor: '#e6f7ff', borderLeft: '4px solid #1890ff', width: '100%' }}>
                <Flex justify="space-between" align="center">
                  <Title level={4} style={{ color: '#1890ff', margin: 0 }}>优势 (Strengths)</Title>
                </Flex>
                <div style={{ marginTop: "16px" }}>
                  <ul className='goalList'>
                    {weekAnalysisData.swot?.strengths.map((item, index) => (
                      <li key={index} className="goalItem">
                        <div className="goalDot"></div>
                        <Text>{item}</Text>
                      </li>
                    ))}
                  </ul>

                </div>
              </Card>
            </Col>
            <Col span={12} style={{ display: 'flex' }}>
              <Card data-test-id="week-analysis-swot-weaknesses" style={{ backgroundColor: '#fff1f0', borderLeft: '4px solid #f5222d', width: '100%' }}>
                <Flex justify="space-between" align="center">
                  <Title level={4} style={{ color: '#f5222d', margin: 0 }}>劣势 (Weaknesses)</Title>
                </Flex>
                <div style={{ marginTop: "16px" }}>
                  <ul className='goalList'>
                    {weekAnalysisData.swot?.weaknesses.map((item, index) => (
                      <li key={index} className="goalItem">
                        <div className="goalDot"></div>
                        <Text>{item}</Text>
                      </li>
                    ))}
                  </ul>

                </div>
              </Card>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: '16px', display: 'flex' }}>
            <Col span={12} style={{ display: 'flex' }}>
              <Card data-test-id="week-analysis-swot-opportunities"  style={{ backgroundColor: '#f6ffed', borderLeft: '4px solid #52c41a', width: '100%' }}>
                <Flex justify="space-between" align="center">
                  <Title level={4} style={{ color: '#52c41a', margin: 0 }}>机会 (Opportunities)</Title>
                </Flex>
                <div style={{ marginTop: "16px" }}>
                  <ul className='goalList'>
                    {weekAnalysisData.swot?.opportunities.map((item, index) => (
                      <li key={index} className="goalItem">
                        <div className="goalDot"></div>
                        <Text>{item}</Text>
                      </li>
                    ))}
                  </ul>

                </div>
              </Card>
            </Col>
            <Col span={12} style={{ display: 'flex' }}>
              <Card data-test-id="week-analysis-swot-threats" style={{ backgroundColor: '#fff2e8', borderLeft: '4px solid #fa8c16', width: '100%' }}>
                <Flex justify="space-between" align="center">
                  <Title level={4} style={{ color: '#fa8c16', margin: 0 }}>威胁 (Threats)</Title>
                </Flex>
                <div style={{ marginTop: "16px" }}>
                  <ul className='goalList'>
                    {weekAnalysisData.swot?.threats.map((item, index) => (
                      <li key={index} className="goalItem">
                        <div className="goalDot"></div>
                        <Text>{item}</Text>
                      </li>
                    ))}
                  </ul>

                </div>
              </Card>
            </Col>
          </Row>
        </div>

        {/* 关键日期 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} style={{ margin: 0 }}>关键日期</Title>
        </Flex>
        <Space direction="vertical" size={'middle'} style={{ width: '100%' }}>
          {weekAnalysisData.key_dates && weekAnalysisData.key_dates.length > 0 ? (
            weekAnalysisData.key_dates?.map((date, index) => (
              <Card data-test-id="week-analysis-key-dates" className="card" key={index}>
                <Row gutter={4} style={{ marginBottom: 24 }}>
                  <Col span={24}>
                    <Flex justify="space-between" align="center">
                      <Title level={4} style={{ margin: 0 }}>{date.name}</Title>
                    </Flex>
                    <Text style={{ fontSize: "14px", color: '#666', marginTop: 8, display: 'block' }}>{date.expected_impact.rationale}</Text>
                  </Col>
                </Row>
                <Row gutter={[24, 16]}>
                  <Col xs={24} sm={12} md={6}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>日期范围</Text>
                    </div>
                    <Text style={{ fontSize: "16px" }}>{date.start_date} ~ {date.end_date}</Text>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>类型</Text>
                    </div>
                    <Space wrap>
                      {date.type.map((type, i) => (
                        <Tag style={{ fontSize: "16px" }} key={i} color={type === 'peak' ? 'red' : 'blue'}>
                          {type === 'peak' ? '高峰期' : type === 'other' ? '其他' : type}
                        </Tag>
                      ))}
                    </Space>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>重要性</Text>
                    </div>
                    <Tag style={{ fontSize: "16px" }} color={date.significance === 'high' ? 'red' : date.significance === 'medium' ? 'orange' : 'blue'}>
                      {date.significance === 'high' ? '高' : date.significance === 'medium' ? '中' : '低'}
                    </Tag>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>预期影响</Text>
                    </div>
                    <Space wrap>
                      <Tag style={{ fontSize: "16px" }} color={date.expected_impact.traffic === 'high' ? 'red' : date.expected_impact.traffic === 'medium' ? 'orange' : 'blue'}>
                        流量: {date.expected_impact.traffic === 'high' ? '高' : date.expected_impact.traffic === 'medium' ? '中' : '低'}
                      </Tag>
                      <Tag style={{ fontSize: "16px" }} color={date.expected_impact.conversion === 'high' ? 'red' : date.expected_impact.conversion === 'medium' ? 'orange' : 'blue'}>
                        转化: {date.expected_impact.conversion === 'high' ? '高' : date.expected_impact.conversion === 'medium' ? '中' : '低'}
                      </Tag>
                      <Tag style={{ fontSize: "16px" }} color={date.expected_impact.competition === 'high' ? 'red' : date.expected_impact.competition === 'medium' ? 'orange' : 'blue'}>
                        竞争: {date.expected_impact.competition === 'high' ? '高' : date.expected_impact.competition === 'medium' ? '中' : '低'}
                      </Tag>
                    </Space>
                  </Col>
                </Row>
                <Row style={{ marginTop: 24 }}>
                  <Col span={24}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>策略建议</Text>
                    </div>
                    <Text style={{ fontSize: "16px" }}>{date.strategy}</Text>
                  </Col>
                </Row>

              </Card>
            ))
          ) : (
            <div style={{ padding: '24px 0px 24px 0px' }}>
              <Text style={{ fontSize: '16px', color: '#666' }}>本周没有需要重点关注的特殊日期</Text>
            </div>
          )}
        </Space>

        {/* 投放策略板块 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} style={{ margin: 0 }}>广告策略建议</Title>
        </Flex>

        {/* 广告策略建议 */}
        <WeekAnalysisCard ads_suggestion={weekAnalysisData.ads_suggestion} />

        {/* 每周投放策略 */}
        {
          props.type === 'month' && <Card data-test-id="week-analysis-weekly-strategy" className='card' style={{ marginTop: '16px' }}>
            <Flex justify="space-between" align="center">
              <Title level={4} style={{ margin: 0 }}>每周投放策略</Title>
            </Flex>
            <Table style={{ marginTop: 16 }} columns={[
              {
                title: '日期',
                dataIndex: 'week_start_date',
                key: 'week_start_date',
                width: '200px',
              },
              {
                title: '策略',
                dataIndex: 'strategy',
                key: 'strategy',
              },
            ]} dataSource={weekAnalysisData.ads_suggestion.weekly_strategy} pagination={false} />
          </Card>
        }

        {/* 运营建议板块 */}
        <Flex justify="space-between" align="center" style={{ marginTop: "48px", marginBottom: "0.8em" }}>
          <Title level={3} style={{ margin: 0 }}>运营建议</Title>
        </Flex>
        <Card data-test-id="week-analysis-non-ads-suggestion" className='card'>
          <ul className="goalList">
            {weekAnalysisData.non_ads_suggestion?.map((item, index) => (
              <li key={index} className="goalItem">
                <div className="goalDot"></div>
                <Text>{item}</Text>
              </li>
            ))}
          </ul>

        </Card>

        {/* 参考链接 */}
        {
          (google_searchs?.grounding_chunks && google_searchs.grounding_chunks.length > 0) && <>
            <Flex justify="space-between" align="center" style={{ marginTop: "48px" }}>
              <Title level={3} style={{ margin: 0 }}>参考链接</Title>
            </Flex>
            <Card data-test-id="week-analysis-google-searchs" style={{ marginTop: 16 }}>
              {
                google_searchs?.grounding_chunks?.map((item: any, index: number) => (
                  <div key={index} style={{ marginBottom: 8 }}>
                    {index + 1}.
                    <a href={item.url} target="_blank" rel="noopener noreferrer" style={{ marginLeft: 8 }}>{item.title}</a>
                  </div>
                ))
              }
            </Card>
          </>
        }

        {/* 相关搜索 */}
        {
          google_searchs?.rendered_content && <>
            <Flex justify="space-between" align="center" style={{ marginTop: "48px" }}>
              <Title level={3} style={{ margin: 0 }}>相关搜索</Title>
            </Flex>
            <Card data-test-id="week-analysis-google-searchs" style={{ marginTop: 16 }}>
              <div dangerouslySetInnerHTML={{
                __html: google_searchs?.rendered_content.replace(
                  /<a /g,
                  '<a target="_blank" rel="noopener noreferrer" '
                )
              }} />
            </Card>
          </>
        }

        {/* 修订记录 */}
        <RevisionHistory
          revision_history={weekAnalysisData?.revision_history}
        />
      </>
    );
  }
  const viewMonthStrategy = () => {
    const title = `月市场分析报告（${ajaxData.start_date || ''}~${ajaxData.end_date || ''}）`;
    const modalData: WeekMonthReportModalData = {
      current_time: ajaxData.current_time,
      job_id: ajaxData.job_id,
    };
    openMonthReportModal(modalData, title);
  };

  return (
    <div className='report-modal-div'>
      <div className='report-modal-content'>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '24px', marginBottom: '24px', gap: 16 }}>
          <div style={{ flex: 1, overflow: 'hidden' }}>
            <DateNavigator
              initialDate={ajaxData.start_date}
              current_time={ajaxData.current_time}
              job_id={props.type === 'week' ? 'market_report_week' : 'market_report_month'}
              dateType={props.type === 'week' ? 'range' : 'single'}
              es_id={ajaxData.es_id}
              onDateChange={handleDateChange}
              onVersionChange={handleVersionChange}
            />
          </div>
          {ajaxData.can_edit && showControlBtn && <Button onClick={viewMonthStrategy}><EditOutlined />修改策略</Button>}
          <FeedbackButton
            feedbackParams={{
              parent_asin: parent_asin,
              profile_id: profile_id,
              job_id: job_id,
              es_id: ajaxData.es_id,
              current_time: current_time || '',
            }}
          />
        </div>

        {
          loading ?
            <Loading />
            :
            contentCards
        }

      </div>

      {showReviewArea && ajaxData.can_edit && (
        <ConfirmationSection
          report_status={ajaxData.report_status}
          value={aiFeedbackContent}
          onChange={(e) => setAiFeedbackContent(e.target.value)}
          onApprove={handleApprove}
          onModify={handleModify}
          minRows={6}
          maxRows={8}
          placeholder='请输入修改意见，每行一条。AI将根据您的意见进行修订。
  例如：
  ● 7月到10月为本产品的销售旺季，市场流量上升
  ● 将总体策略更改为激进
  ● 主要目标销售额增长25%，ACOS控制在30%以内
  ● 基于上述建议重新制定市场报告和广告策略'
        />
      )}
    </div>
  );
};

export default MonthAnalysisContent;
