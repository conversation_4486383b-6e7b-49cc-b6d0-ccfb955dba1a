import React, { useState } from 'react';
import { Button, Flex, Typography, Modal, message, Space, Image } from 'antd';
import { rerunReport } from '@/services/ibidder_api/operation';
import EmptyState from '../EmptyState';

const { Text, Title } = Typography;

interface ReportFailureStateProps {
  /** 重新生成报告的参数 */
  rerunParams: {
    asins: string[];
    country: string;
    current_time: string;
    es_id: string;
    job_id: string;
    parent_asin: string;
    profile_id: string;
    role: string;
  };
  /** 重新生成成功后的回调 */
  onRerunSuccess: () => void;
  /** 是否显示重新生成功能（只有第一个日期时显示） */
  showRerunFeature?: boolean;
}

const ReportFailureState: React.FC<ReportFailureStateProps> = ({
  rerunParams,
  onRerunSuccess,
  showRerunFeature = true
}) => {
  const [loading, setLoading] = useState(false);
  const [qrModalVisible, setQrModalVisible] = useState(false);

  // 处理重新生成
  const handleRerun = async () => {
    try {
      setLoading(true);
      const res = await rerunReport(rerunParams);

      if (res.code === 200) {
        onRerunSuccess();
      } else {
        message.error(res.message || '重新生成失败，请重试');
      }
    } catch (error) {
      message.error('重新生成失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 显示企业微信二维码弹框
  const showQrModal = () => {
    setQrModalVisible(true);
  };

  return (
    <Flex
      align='center'
      style={{
        flexDirection: 'column',
        gap: 24,
        marginBottom: '10vh',
        padding: '0 24px'
      }}
    >
      {/* 失败图标 */}
      <EmptyState
        imageWidth={180}
        text=''
        style={{
          marginTop: '10vh',
          marginBottom: 0,
        }}
      />
      <Title level={3} style={{ margin: 0, textAlign: 'center' }}>
        广告策略报告生成失败
      </Title>

      {/* 失败原因列表 */}
      <Space direction='vertical' style={{ maxWidth: '600px', width: '100%' }}>
        <Text style={{ fontSize: '16px', color: '#666', textAlign: 'left', maxWidth: '600px' }}>
          未能成功生成报告。这通常由以下几种情况导致，您可以尝试解决：
        </Text>
        <div style={{ marginBottom: '8px' }}>
          <Text strong style={{ fontSize: '16px' }}>• 数据正在同步中</Text>
          <div style={{ marginTop: '8px', marginLeft: '16px' }}>
            <Text style={{ color: '#666' }}>原因：店铺授权时间过短，数据尚未完全同步。</Text>
            <br />
            <Text style={{ color: '#666' }}>
              解决方案：请等待数据同步完成（通常需要几分钟），然后点击
              <Text strong style={{ color: '#1890ff' }}>【重新生成】</Text> 按钮。
            </Text>
          </div>
        </div>

        <div style={{ marginBottom: '8px' }}>
          <Text strong style={{ fontSize: '16px' }}>• 广告历史数据不足</Text>
          <div style={{ marginTop: '8px', marginLeft: '16px' }}>
            <Text style={{ color: '#666' }}>原因：AI策略模型需要基于一定量的历史数据进行分析。</Text>
            <br />
            <Text style={{ color: '#666' }}>
              解决方案：为保证策略质量，请选择预算稳定在 $50以上 的产品进行投放分析。
            </Text>
          </div>
        </div>

        <div style={{ marginBottom: '24px' }}>
          <Text strong style={{ fontSize: '16px' }}>• 网络稳定性</Text>
          <div style={{ marginTop: '8px', marginLeft: '16px' }}>
            <Text style={{ color: '#666' }}>原因：网络连接不稳定或中断。</Text>
            <br />
            <Text style={{ color: '#666' }}>
              解决方案：请确保网络连接正常后，刷新当前页面或点击
              <Text strong style={{ color: '#1890ff' }}>【重新生成】</Text>。
            </Text>
          </div>
        </div>
        
        <Text style={{ fontSize: '16px', color: '#666', textAlign: 'center' }}>
          若以上情况都不符合，请点击下方按钮
          <Text strong style={{ color: '#f5222d' }}>【联系客服】</Text>，我们将及时为您解决问题。
        </Text>
      </Space>

      {/* 操作按钮 */}
      <Flex gap={16}>
        {showRerunFeature && (
          <Button
            type="primary"
            size="large"
            loading={loading}
            onClick={handleRerun}
            style={{
              backgroundColor: '#1f2937',
              borderColor: '#1f2937',
              minWidth: '120px',
              borderRadius: '18px'
            }}
          >
            重新生成
          </Button>
        )}
        <Button
          type='primary'
          size="large"
          onClick={showQrModal}
          style={{ minWidth: '120px', borderRadius: '18px' }}
        >
          联系客服
        </Button>
      </Flex>

      {/* 企业微信二维码弹框 */}
      <Modal
        title={null}
        open={qrModalVisible}
        onCancel={() => setQrModalVisible(false)}
        footer={null}
        width={400}
        centered
      >
        <Flex align="center" justify="center" style={{ flexDirection: 'column', padding: '20px 0' }}>
          <Text style={{ textAlign: 'center' }}>微信添加客服企微</Text>
          <br />
          <div style={{
            width: 200,
            height: 200,
            backgroundColor: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: 16,
            border: '1px solid #d9d9d9'
          }}>
            {/* 这里应该放置实际的企业微信二维码图片 */}
            <Image src={require('@/assets/images/wechat_sls_qr.png')} alt="企业微信二维码" />
          </div>
        </Flex>
      </Modal>
    </Flex>
  );
};

export default ReportFailureState;