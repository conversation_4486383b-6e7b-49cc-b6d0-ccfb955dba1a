import React from 'react';
import { Card, Col, Row, Typography, Progress, Tooltip, Space, Flex } from 'antd';
import styles from './index.less';
import { processNumberOrString } from '@/utils/bus';
import { getDiffColor } from '@/utils/common';
import { useModel } from 'umi';
import AdsTrendChartCat from '../AdsTrendChartCat';

const { Title, Text } = Typography;

interface DailyProgressAnalysisProps {
  data?: API.DayProgressAnalysis;
  overallRationale?: string;
  time: string;
  hodBidding: API.TAds_data_hod[];
}

const DailyProgressAnalysis: React.FC<DailyProgressAnalysisProps> = (props) => {
  const { data, overallRationale, time, hodBidding } = props;
  const { productInfo } = useModel('productInfo');
  if (!data) {
    return null;
  }
  const currency = productInfo?.currency || '$';

  const { budget, sales, acos, cvr, day_performance_vs_target, key_observations } = data;

  const getStatusText = (status: string) => {
    switch (status) {
      case 'on_track':
        return '正常';
      case 'behind':
        return '落后';
      case 'ahead':
        return '超前';
      default:
        return status;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'on_track':
        return styles.onTrack;
      case 'ahead':
        return styles.ahead;
      case 'behind':
        return styles.behind;
      default:
        return ''; // behind 使用默认红色
    }
  };

  const parsePercentage = (percentStr: string): number => {
    const match = percentStr.match(/(\d+(?:\.\d+)?)/);
    return match ? parseFloat(match[1]) : 0;
  };

  const getProgressColor = (percent: number): string => {
    return percent < 100 ? '#1890ff' : '#ff4d4f';
  };

  // 计算剩余预算
  const calculateRemainingBudget = (): string => {
    if (
      budget.day_budget === null || budget.day_budget === undefined || budget.day_budget === 'N/A' || budget.day_budget === ''
      ||
      budget.day_budget_spend === null || budget.day_budget_spend === undefined || budget.day_budget_spend === 'N/A' || budget.day_budget_spend === ''
    ) {
      return `-`;
    }

    // 如果接口没有返回剩余预算，自己计算
    const dayBudget = parseFloat(String(budget.day_budget)) || 0;
    const daySpend = parseFloat(String(budget.day_budget_spend)) || 0;
    const remaining = dayBudget - daySpend;
    return `${currency}${remaining.toFixed(2)}`;
  };

  // 计算ACOS差距
  const calculateAcosDiff = (): string => {
    if (acos.diff !== null && acos.diff !== undefined && acos.diff !== 'N/A' && acos.diff !== '') {
      return String(processNumberOrString(acos.diff, '%'));
    }

    // 如果接口没有返回差距，自己计算
    const current = parseFloat(String(processNumberOrString(acos.current))) || 0;
    const target = parseFloat(String(processNumberOrString(acos.target))) || 0;
    const diff = current - target;
    const sign = diff >= 0 ? '+' : '';
    return `${sign}${diff.toFixed(2)}%`;
  };

  // 计算CVR差距
  const calculateCvrDiff = (): string => {
    if (cvr.diff !== null && cvr.diff !== undefined && cvr.diff !== 'N/A' && cvr.diff !== '') {
      return String(processNumberOrString(cvr.diff, '%'));
    }

    // 如果接口没有返回差距，自己计算
    const current = parseFloat(String(processNumberOrString(cvr.current))) || 0;
    const target = parseFloat(String(processNumberOrString(cvr.target))) || 0;
    const diff = current - target;
    const sign = diff >= 0 ? '+' : '';
    return `${sign}${diff.toFixed(2)}%`;
  };

  return (
    <div>
      <Space>
        <Title level={3}>当日进度评估</Title>
        {time && <Text type="secondary">数据截止至 {time} (站点时间)</Text>}
      </Space>
      <Row gutter={[16, 16]} className='card-row'>
        <Col span={6}>
          <Card data-test-id="daily-progress-analysis-current-spend" className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前花费</Title>
            </div>
            <div className={styles.metricValue}>{`${currency}${budget.day_budget_spend}`}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Tooltip title={budget.current_spend_progress}>
                  <Progress
                    percent={parsePercentage(budget.current_spend_progress)}
                    size="small"
                    showInfo={false}
                    format={() => budget.current_spend_progress}
                    strokeColor={getProgressColor(parsePercentage(budget.current_spend_progress))}
                  />
                </Tooltip>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">日预算</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{`${currency}${budget.day_budget}`}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">剩余</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{calculateRemainingBudget()}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card data-test-id="daily-progress-analysis-current-sales" className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前销售</Title>
            </div>
            <div className={styles.metricValue}>{`${currency}${sales.sales_current}`}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Tooltip title={sales.current_progress}>
                  <Progress
                    percent={parsePercentage(sales.current_progress)}
                    size="small"
                    showInfo={false}
                    format={() => sales.current_progress}
                    strokeColor={getProgressColor(parsePercentage(sales.current_progress))}
                  />
                </Tooltip>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">目标销售</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{`${currency}${sales.sales_target}`}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card data-test-id="daily-progress-analysis-current-acos" className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前ACOS</Title>
            </div>
            <div className={styles.metricValue}>{processNumberOrString(acos.current, '%')}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Text type="secondary">目标ACoS</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{processNumberOrString(acos.target, '%')}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">差距</Text>
                <Text style={{ fontWeight: '500', fontSize: 16, color: getDiffColor(calculateAcosDiff()) }}>{calculateAcosDiff()}</Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card data-test-id="daily-progress-analysis-current-cvr" className={`${styles.metricCard} card`}>
            <div className={styles.metricHeader}>
              <Title level={5} className={styles.metricTitle}>当前CVR</Title>
            </div>
            <div className={styles.metricValue}>{processNumberOrString(cvr.current, '%')}</div>
            <div className={styles.metricDetails}>
              <div className={styles.detailItem}>
                <Text type="secondary">目标CVR</Text>
                <Text style={{ fontWeight: '500', fontSize: 16 }}>{processNumberOrString(cvr.target, '%')}</Text>
              </div>
              <div className={styles.detailItem}>
                <Text type="secondary">差距</Text>
                <Text style={{ fontWeight: '500', fontSize: 16, color: getDiffColor(calculateCvrDiff()) }}>{calculateCvrDiff()}</Text>
              </div>
            </div>
          </Card>
        </Col>

        <Col span={12}>
          <Card data-test-id="daily-progress-analysis-overall-progress" className="card">
            <div className={styles.mainStrategy}>
              <div className={styles.strategyContent}>
                <Title level={5}>总体进度</Title>
                <Title level={3} className={`${styles.strategyValue} ${getStatusClass(day_performance_vs_target)}`}>
                  {getStatusText(day_performance_vs_target)}
                </Title>
                <ul className="goalList" style={{ overflowY: 'auto', maxHeight: '180px' }}>
                  {key_observations.map((desc: string, index: number) => (
                    <li key={index} className="goalItem">
                      {desc && <div className="goalDot"></div>}
                      <Text style={{ textAlign: 'left', lineHeight: '1.5' }}>{desc}</Text>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card data-test-id="daily-progress-analysis-adjustment-strategy" className="card">
            <div className={styles.mainStrategy}>
              <div className={styles.strategyContent}>
                <Title level={5} style={{ marginBottom: '12px' }}>调整思路</Title>
                <Text style={{ lineHeight: '1.8', color: '#0C1D23' }}>{overallRationale}</Text>
              </div>
            </div>
          </Card>
        </Col>

        {
          hodBidding.length > 0 && (
            <Col span={24}>
              <Card data-test-id="daily-progress-analysis-ads-trend" className="card">
                <Flex justify="space-between" align="center">
                  <Title level={5} style={{ marginBottom: 16 }}>日广告表现趋势</Title>
                </Flex>
                <div style={{ width: '100%', height: '300px' }} >
                  <AdsTrendChartCat
                    currency={currency}
                    data={{
                      xAxisData: hodBidding.map((item) => item.hod + ':00'),
                      spend: hodBidding.map((item) => Number(item.spend)),
                      sales: hodBidding.map((item) => Number(item.sales)),
                      acos: hodBidding.map((item) => Number(item.acos.replace('%', ''))),
                      cvr: hodBidding.map((item) => Number(item.cvr.replace('%', ''))),
                      // 添加累计值 - 使用第一个hodBidding项的total字段（因为total值对所有时间点都是相同的）
                      spend_totle: hodBidding.map((item) => Number(item.spend_totle)),
                      sales_totle: hodBidding.map((item) => Number(item.sales_totle)),
                      cvr_totle: hodBidding.map((item) => item.cvr_totle),
                      acos_totle: hodBidding.map((item) => item.acos_totle),
                      orders: hodBidding.map((item) => Number(item.orders)),
                      orders_totle: hodBidding.map((item) => Number(item.orders_totle)),
                      clicks: hodBidding.map((item) => Number(item.clicks)),
                      clicks_totle: hodBidding.map((item) => Number(item.clicks_totle)),
                    }}
                    chartOption={{
                      axisLabel: {
                        interval: 0,
                        rotate: 45
                      }
                    }}
                  />
                </div>
              </Card>
            </Col>
          )
        }
      </Row>

    </div>
  );
};

export default DailyProgressAnalysis; 