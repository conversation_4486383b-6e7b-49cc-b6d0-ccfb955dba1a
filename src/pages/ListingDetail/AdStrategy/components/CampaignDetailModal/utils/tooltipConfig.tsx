import React from 'react';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

// Tooltip描述映射表
export const TOOLTIP_DESCRIPTIONS = {
  orders: "订单数量是指买家在点击您广告后提交的亚马逊订单数量。\n商品推广： 7 天内购买的推广商品及库存中其他商品的订单数量。\n品牌推广： 14 天内在亚马逊及第三方卖家处售出的推广商品及同品牌所有商品的订单数量。\n在亚马逊系统中，您的订单数据可能需要 12 小时才会更新。\n因此，我们建议您使用2天前的订单数量去评估广告活动绩效。\n付款失败的订单数量和 72 小时内取消的订单数量将从订单总量中删除。\n该广告订单数：包括otherasin的订单。",
  budget: "广告活动的预算设置",
  spend: "广告活动的总点击费用\n注意：一旦识别出无效点击，亚马逊系统最多会在 3 天内从您的支出统计数据中删除这些点击记录。日期范围（含过去 3 天内的支出）可能因点击和支出失效而有所调整",
  sales: "销售额是在运行某种广告活动期间的指定时间范围内，因广告被点击而向买家售出的商品的价值总额。\n商品推广广告： 7 天内售出的推广商品及库存中其他商品的销售额。\n品牌推广： 14 天内在亚马逊及第三方卖家处售出的推广商品及同品牌其他商品的销售额。\n在亚马逊系统中，您的销售数据可能需要 12 小时才会更新。\n因此，我们建议您使用2天前的销售额去评估广告活动绩效。\n未成功支付的款项和 72 小时内取消的订单的金额将从销售总额中删除。",
  acos: "ACOS 是在指定时间范围内，某种类型的广告活动因为广告被点击而产生的支出在由广告产生的销售额中所占的百分比。\nACoS=花费/销售额*100%\n商品推广广告： ACOS 包括 7 天内被购买的推广商品及库存中其他商品带来的由广告产生的销售额。\n品牌推广： ACOS 包括 14 天内被亚马逊或第三方卖家售出的推广商品及同品牌其他商品带来的由广告产生的销售额。\n在亚马逊系统中，您的销售数据可能需要 12 小时才会更新。\n因此，我们建议您使用2天前的销售额去评估广告活动绩效。\n未成功支付的款项和 72 小时内取消的订单的金额将从销售总额中删除。",
  cvr: "订单数占点击次数的百分比。转化率(CVR)=订单/点击次数*100%",
  ctr: "商品广告展示时买家点击频率的比率。点击率(CTR)=点击量/曝光量*100%\n注意：一旦识别出无效点击，亚马逊系统最多会在 3 天内从您的支出统计数据中删除这些点击记录。日期范围（含过去 3 天内的支出）可能因点击和支出失效而有所调整",
  impressions: "广告被展示的次数",
  clicks: "广告被点击的次数\n注意：一旦识别出无效点击，亚马逊系统最多会在 3 天内予以删除",
  click_ratio: "指单条记录的点击占查询结果总点击的百分比",
  cpc: "这是您为每次广告点击支付的平均费用\n注意：一旦识别出无效点击，亚马逊系统最多会在 3 天内从您的支出统计数据中删除这些点击记录。日期范围（含过去 3 天内的支出）可能因点击和支出失效而有所调整",
  topOfSearchImpressionShare: "指搜索结果首页首位展示量份额(IS)，广告活动获得的搜索结果顶部展示量占其获得的所有符合条件的搜索结果顶部展示量的百分比。符合条件与否由各种因素所决定，包括广告活动状态和展示状态；该指标适用于品牌推广活动和商品推广活动；对于商品推广，该指标提供了搜索结果顶部（首页）的展示量份额。\n报告中默认显示所选时间段内的最高份额。",
  cpa: "平均到每笔广告订单上的花费金额。每笔订单花费=Spend/Orders",
  units: "指通过广告成交的销售件数",
  sku_orders: "用户点击广告后，直接购买了广告MSKU所产生的订单",
  sku_sales: "用户点击广告后，直接购买了广告MSKU所产生的销售额",
  spend_ratio: "指单条记录的花费占查询结果总花费的百分比",
  sales_ratio: "指单条记录的销售额占查询结果总销售额的百分比",
} as const;

// 创建带tooltip的标题组件
export const createTitleWithTooltip = (title: string | React.ReactNode, tooltipKey: keyof typeof TOOLTIP_DESCRIPTIONS) => {
  return (
    <span>
      {title}
      <Tooltip 
        title={TOOLTIP_DESCRIPTIONS[tooltipKey]} 
        overlayStyle={{ whiteSpace: 'pre-wrap', maxWidth: 470 }}
      >
        <QuestionCircleOutlined style={{ color: '#86909C', fontSize: '14px', marginLeft: '4px' }} />
      </Tooltip>
    </span>
  );
};