import React, { useState } from 'react';
import { Row, Col, Button, Card, Typography } from 'antd';
import styles from './style.module.less';
import logo from '@/assets/images/logo.svg';
import { Link } from '@umijs/max';
import { loginPath, registerPath } from '../../../config/routes';
import { sourceImageUrl } from '@/utils/common';
import { Footer } from '@/components';
import { AuthUtils } from '@/utils/auth';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const [selectedCard, setSelectedCard] = useState<number>(1);
  const isLogin = AuthUtils.isLoggedIn();

  const goToRegister = () => {
    if (isLogin) {
      window.location.href = '/admin/listing';
    } else {
      window.location.href = loginPath;
    }
  };

  return (
    <div className={styles.homePage}>
      {/* 头部导航 */}
      <div className={styles.header}>
        <Row justify="space-between" align="middle" style={{ padding: '0 40px', height: '64px' }}>
          <Col>
            <div className={styles.logo} onClick={() => window.scrollTo(0, 0)} style={{ cursor: 'pointer' }}>
              <img src={logo} alt="FlyPower.AI" height={40} />
            </div>
          </Col>
          <Col>
            <Row gutter={32} align="middle">
              <Col>
                <Link to={registerPath}>
                  <Button type="primary">
                    立即注册
                  </Button>
                </Link>
              </Col>
              <Col>
                <a onClick={goToRegister} className={styles.loginLink}>{isLogin ? '立即使用' : '登录'}</a>
              </Col>
            </Row>
          </Col>
        </Row>
      </div>

      <div className={styles.content}>
        {/* Hero Section */}
        <div className={styles.heroSection}>
          <Row align="middle" justify="center">
            <Col lg={12} md={24}>
              <div className={styles.heroText}>
                <Title style={{ fontSize: 50 }} level={1}>增长<br />由AI驱动</Title>
                <Paragraph className={styles.paragraph} style={{ fontSize: 20, paddingRight: '1em' }}>FlyPower AI广告引擎，为亚马逊卖家注入全新生产力，一站式提升全链路、多渠道运营效率，驱动业务持续增长。</Paragraph>
                <Button onClick={goToRegister} type="primary" size="large" className={styles.heroButton}>立即使用</Button>
              </div>
            </Col>
            <Col lg={12} md={24}>
              <img src={sourceImageUrl('home/hero-section.svg')} alt="AI Driven Growth" style={{ maxWidth: '100%' }} />
            </Col>
          </Row>
        </div>

        {/* Features Section */}
        <div className={styles.featuresSection} >
          <Row gutter={[48, 48]} justify="center">
            <Col md={8} sm={24} className={styles.featureCard}>
              <div className={styles.featureContent}>
                <img src={sourceImageUrl('home/feature-intelligent-engine.svg')} alt="智能技术引擎" className={styles.featureImage} />
                <Title level={4}>智能技术引擎<br />驱动广告自动化盈利</Title>
                <Paragraph className={styles.paragraph}>AI 智能调优与人机协同无缝结合，将您的经验注入 AI 大脑，让机器为您执行最优投放策略，轻松驶入增长快车道。</Paragraph>
              </div>
            </Col>
            <Col md={8} sm={24} className={styles.featureCard}>
              <div className={styles.featureContent}>
                <img src={sourceImageUrl('home/feature-attribution-model.svg')} alt="科学归因模型" className={styles.featureImage} />
                <Title level={4}>科学归因模型<br />让每一分钱都清晰可见</Title>
                <Paragraph className={styles.paragraph}>我们基于您店铺的历史销售与广告数据，通过算法模型，帮助您评估广告活动对整体销售的贡献。让您更清晰地了解广告花费的效果，用数据支持您的决策，优化广告ROI。</Paragraph>
              </div>
            </Col>
            <Col md={8} sm={24} className={styles.featureCard}>
              <div className={styles.featureContent}>
                <img src={sourceImageUrl('home/feature-market-insight.svg')} alt="全景市场洞察" className={styles.featureImage} />
                <Title level={4}>广告表现分析<br />优化您的投放决策</Title>
                <Paragraph className={styles.paragraph}>深入分析您授权的广告活动数据，清晰展示各项核心指标（如ACOS、CVR、点击量）的表现。帮助您精准评估广告效果，识别高潜力商品与关键词，持续优化您的广告ROI。</Paragraph>
              </div>
            </Col>
          </Row>
        </div>

        {/* AI Responsibility Section */}
        <div className={styles.aiResponsibilitySection}>
          <Row align="middle" justify="center">
            <Col lg={11} md={24}>
              <img src={sourceImageUrl('home/ai-responsibility-section.svg')} alt="AI 负责执行" style={{ maxWidth: '100%' }} />
            </Col>
            <Col lg={13} md={24}>
              <div className={styles.sectionText}>
                <Title level={2}>您只需掌控全局<br />剩下的交给AI</Title>
                <Paragraph className={styles.paragraph}>别让AI取代您的经验，让它放大您的智慧。我们行业首创的人机协同模式：</Paragraph>
                <ul className={styles.paragraph}>
                  <li>经验赋能AI：您的运营策略指导AI，让机器决策更懂您的业务。</li>
                  <li>数据辅助决策：AI为您整理并呈现您账户内的核心数据，助您做出更自信的判断。</li>
                  <li style={{ listStyle: 'none' }}>这不是简单的自动化，而是将您的经验与AI能力相乘，让您从重复劳动中解放，聚焦于真正的商业增长。</li>
                </ul>
                <br />
                <Link to={registerPath} className={styles.learnMore}>了解更多</Link>
              </div>
            </Col>
          </Row>
        </div>

        {/* Bidding Section */}
        <div className={styles.biddingSection}>
          <Row align="middle" justify="center">
            <Col lg={13} md={24}>
              <div className={styles.sectionText}>
                <Title level={2}>告别人工猜测<br /> 让投放决策精准如手术刀</Title>
                <Paragraph className={styles.paragraph}>我们的AI引擎基于您授权的账户数据和尖端算法，深度理解您的营销目标。</Paragraph>
                <ul className={styles.paragraph}>
                  <li>动态预算分配：将预算实时倾斜给高效益的广告活动。</li>
                  <li>一站式管理：覆盖广告创建、优化到广告表现分析的全流程。</li>
                  <li>智能竞价与定向：自动锁定高转化流量。</li>
                  <li style={{ listStyle: 'none' }}>FlyPower AI为您突破人效极限，让每一分钱都创造最大价值。</li>
                </ul>
                <br />
                <Link to={registerPath} className={styles.learnMore}>了解更多</Link>
              </div>
            </Col>
            <Col lg={11} md={24} style={{  textAlign: 'right' }}>
              <img src={sourceImageUrl('home/bidding-section.svg')} alt="投放决策" style={{ maxWidth: '100%' }} />
            </Col>
          </Row>
        </div>

        {/* Automation Section */}
        <div className={styles.automationSection}>
          <Row align="middle" justify="center">
            <Col lg={11} md={24}>
              <img src={sourceImageUrl('home/automation-section.svg')} alt="自动操盘手" style={{ maxWidth: '100%' }} />
            </Col>
            <Col lg={13} md={24}>
              <div className={styles.sectionText}>
                <Title level={2}>您的7x24小时自动操盘手<br />精准执行，决胜千里</Title>
                <Paragraph className={styles.paragraph}>还在手动调价，被对手偷袭？告别繁琐，让AI成为您的全天候运营专家</Paragraph>
                <ul className={styles.paragraph}>
                  <li>智能竞价：AI根据您设定的广告目标，以15分钟/次的频率分析您广告活动的实时表现，并自动调整出价，力求将您的广告展示在更有效的位置。</li>
                  <li>分时智控，预算花在刀刃上：独创分时模板，根据小时级出单量，自动调整预算与竞价。高峰期猛攻，低谷期节流，最大化您的资金效率和广告ROI。</li>
                </ul>
                <br />
                <Link to={registerPath} className={styles.learnMore}>了解更多</Link>
              </div>
            </Col>
          </Row>
        </div>

        {/* Pricing Section */}
        {/* <div className={styles.pricingSection}>
          <Title level={1} style={{ textAlign: 'left', fontSize: 60, marginBottom: '80px' }}>开启增长，<br />定制专属方案</Title>
          <Row gutter={[16, 32]} justify="center">
            <Col lg={8} md={24} onMouseEnter={() => setHoveredCard(0)} onMouseLeave={() => setHoveredCard(null)} onClick={() => setSelectedCard(0)}>
              <Card className={`${styles.priceCard} ${hoveredCard === 0 || (hoveredCard === null && selectedCard === 0) ? styles.featuredCard : ''}`}>
                <Title level={4}>基础版</Title>
                <Title level={2} className={styles.price}>¥49,800/年</Title>
                <ul className={styles.featureList}>
                  <li>子账户数量: 10个</li>
                  <li>自动化规则上限: 300条</li>
                  <li>智能排名优化上限: 5条</li>
                  <li>关键词表现追踪上限: 50个</li>
                </ul>
                <Link to={registerPath}><Button block className={styles.priceButton}>购买咨询</Button></Link>
              </Card>
            </Col>
            <Col lg={8} md={24} onMouseEnter={() => setHoveredCard(1)} onMouseLeave={() => setHoveredCard(null)} onClick={() => setSelectedCard(1)}>
              <Card className={`${styles.priceCard} ${hoveredCard === 1 || (hoveredCard === null && selectedCard === 1) ? styles.featuredCard : ''}`}>
                <Title level={4}>卓越版</Title>
                <Title level={2} className={styles.price}>¥129,800/年</Title>
                <ul className={styles.featureList}>
                  <li>子账户数量: 30个</li>
                  <li>自动化规则上限: 1000条</li>
                  <li>智能排名优化上限: 20条</li>
                  <li>关键词表现追踪上限: 400个</li>
                </ul>
                <Link to={registerPath}><Button block className={styles.priceButton}>购买咨询</Button></Link>
              </Card>
            </Col>
            <Col lg={8} md={24} onMouseEnter={() => setHoveredCard(2)} onMouseLeave={() => setHoveredCard(null)} onClick={() => setSelectedCard(2)}>
              <Card className={`${styles.priceCard} ${hoveredCard === 2 || (hoveredCard === null && selectedCard === 2) ? styles.featuredCard : ''}`}>
                <Title level={4}>企业版</Title>
                <Title level={2} className={styles.price}>¥1,880,000/年</Title>
                <ul className={styles.featureList}>
                  <li>子账户数量: 60个</li>
                  <li>自动化规则上限: 无</li>
                  <li>智能排名优化上限: 80条</li>
                  <li>关键词表现追踪上限: 1000个</li>
                  <li>AMC Hub自定义分析模型</li>
                </ul>
                <Link to={registerPath}><Button block className={styles.priceButton}>购买咨询</Button></Link>
              </Card>
            </Col>
          </Row>
        </div> */}

        {/* Privacy Section */}
        {/* <div className={styles.privacySection}>
          <Title level={2} className={styles.privacyTitle}>数据隐私与合规性承诺</Title>
          <Paragraph className={styles.paragraph}>FlyPower.ai 严格遵守亚马逊SP-API的《可接受使用政策》和《数据保护政策》。我们郑重承诺：</Paragraph>
          <ol className={styles.paragraph}>
            <li>官方授权接口：所有亚马逊店铺数据均通过亚马逊官方SP-API接口，在获得卖家明确授权后进行访问。我们不使用任何网络爬虫或其他非官方技术获取亚马逊信息。</li>
            <li>严格数据隔离：我们为每一位卖家的数据提供独立、隔离的存储和处理。我们绝不将不同卖家的数据进行任何形式的汇总、聚合分析、共享或转售。</li>
            <li>数据归您所有：您对自己店铺的数据拥有完全的所有权。我们的工具仅在您的授权范围内，为您分析您自己的数据，以优化您自己的广告活动。</li>
          </ol>
        </div> */}
      </div>
      <Footer/>
    </div>
  );
};

export default HomePage;
