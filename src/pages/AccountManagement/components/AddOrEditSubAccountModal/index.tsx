import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Spin,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import {
  getAccountFilterOptions,
  getShopList,
} from '@/services/ibidder_api/user';
import { countryName } from '@/utils/translationMaps';

interface AddOrEditSubAccountModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
}

const AddOrEditSubAccountModal: React.FC<AddOrEditSubAccountModalProps> = ({
  open,
  onCancel,
  onOk,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [shopList, setShopList] = useState<any[]>([]);
  const [roleOptions, setRoleOptions] = useState<any[]>([]);

  useEffect(() => {
    if (open) {
      setLoading(true);
      Promise.all([getAccountFilterOptions(), getShopList({})])
        .then(([roleRes, shopRes]) => {
          if (roleRes.code === 200) {
            setRoleOptions(roleRes.data.role_map);
          }
          if (shopRes.code === 200) {
            setShopList(shopRes.data.profile_id_list);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [open]);

  const handleOk = () => {
    form.validateFields().then((values) => {
      console.log(values);
      onOk();
    });
  };

  return (
    <Modal
      open={open}
      onCancel={onCancel}
      onOk={handleOk}
      title={null}
      width={1280}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <Form form={form} layout="vertical">
            <div></div>
          <Row gutter={24}>
            <Col span={10}>
              <h3>子账号信息</h3>
              <Form.Item
                label="用户名"
                name="user_name"
                rules={[{ required: true, message: '请输入子账号的昵称' }]}
              >
                <Input placeholder="请输入子账号的昵称" />
              </Form.Item>
              <Form.Item
                label="角色分配"
                name="role_id"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select placeholder="子管理员">
                  {roleOptions.map((role) => (
                    <Select.Option key={role.role_id} value={role.role_id}>
                      {role.role_name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item label="登录手机号" name="phone">
                <Input placeholder="请输入登录手机号" />
              </Form.Item>
              <Form.Item
                label="登录邮箱"
                name="email"
                rules={[{ required: true, message: '请输入登录邮箱' }]}
              >
                <Input placeholder="请输入登录邮箱" />
              </Form.Item>
              <Form.Item
                label="登录密码"
                name="password"
                rules={[{ required: true, message: '请再次输入登录密码' }]}
              >
                <Input.Password placeholder="请再次输入登录密码" />
              </Form.Item>
              <Form.Item
                label="确认密码"
                name="confirm_password"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('两次输入的密码不匹配'),
                      );
                    },
                  }),
                ]}
              >
                <Input.Password placeholder="请确认密码" />
              </Form.Item>
              <Form.Item label="添加到用户分组（可选）" name="group_id">
                <Select placeholder="--" />
              </Form.Item>
            </Col>
            <Col span={14}>
              <h3>数据权限（请选择该成员可访问的店铺）</h3>
              <Form.Item name="profile_ids">
                <Checkbox.Group style={{ width: '100%' }}>
                  {shopList.map((shop) => (
                    <div key={shop.profile_id} style={{ marginBottom: 16 }}>
                      <Checkbox value={shop.profile_id}>{shop.name}</Checkbox>
                    </div>
                  ))}
                </Checkbox.Group>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Spin>
    </Modal>
  );
};

export default AddOrEditSubAccountModal;
