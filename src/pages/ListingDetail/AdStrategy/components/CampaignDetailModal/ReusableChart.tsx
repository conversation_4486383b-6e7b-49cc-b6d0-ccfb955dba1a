import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import { chartColors } from '@/utils/common';

type ReusableChartProps = {
  currency: string;
  dates: string[];
  series: (echarts.SeriesOption & { name: string })[];
  selectedSeries?: string[];
  onSelectedSeriesChange?: (selectedSeries: string[]) => void;
  currencyFormattedSeries: string[];
  percentageFormattedSeries: string[];
};

const ReusableChart: React.FC<ReusableChartProps> = (props) => {
  const {
    currency,
    dates,
    series,
    selectedSeries,
    onSelectedSeriesChange,
    currencyFormattedSeries,
    percentageFormattedSeries,
  } = props;
  const chartRef = useRef<HTMLDivElement>(null);

  const seriesNames = series.map(s => s.name);
  const selectedSeriesRef = useRef<string[]>(selectedSeries || seriesNames.slice(0, 6));

  // 同步外部传入的选中状态
  useEffect(() => {
    if (selectedSeries) {
      selectedSeriesRef.current = [...selectedSeries];
    }
  }, [selectedSeries]);

  useEffect(() => {
    if (chartRef.current) {
      const myChart = echarts.init(chartRef.current);


      // 根据当前选中的系列生成legend的selected配置
      const legendSelected: Record<string, boolean> = {};
      seriesNames.forEach(seriesName => {
        legendSelected[seriesName] = selectedSeriesRef.current.includes(seriesName);
      });

      const option: echarts.EChartsOption = {
        color: chartColors,
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.65)',
          borderColor: '#444',
          textStyle: {
            color: '#fff',
          },
          formatter: (params: any) => {
            let result = `<div style="color:#fff; margin-bottom: 8px;">${params[0].axisValue}</div>`;
            const containerStyle = `display: grid; grid-template-columns: auto auto auto; align-items: center; grid-gap:4px; color: #fff;`;
            let gridContent = '';

            const seriesData: Record<string, any> = {};
            params.forEach((p: any) => {
              seriesData[p.seriesName] = p;
            });

            seriesNames.forEach(seriesName => {
              const param = seriesData[seriesName];
              if (!param) return;

              const { value, color } = param;
              let formattedValue = value;

              if (currencyFormattedSeries.includes(seriesName)) {
                formattedValue = `${currency}${value.toFixed(2)}`;
              } else if (percentageFormattedSeries.includes(seriesName)) {
                formattedValue = `${value.toFixed(2)}%`;
              }

              gridContent += `
                      <div><span style="color:${color};font-size:16px;">●</span> ${seriesName}</div>
                      <div>:</div>
                      <div style="text-align: right; font-weight: bold;">${formattedValue}</div>
                  `;
            });

            result += `<div style="${containerStyle}">${gridContent}</div>`;
            return result;
          }
        },
        legend: {
          data: seriesNames,
          selected: legendSelected,
          top: 'top',
        },
        grid: {
          left: '2%',
          right: '2%',
          bottom: '1%',
          top: 70,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: dates,
        },
        yAxis: [
          {
            type: 'value',
            position: 'left',
            axisLabel: {
              formatter: '{value}',
            },
          },
          {
            type: 'value',
            position: 'right',
            axisLabel: {
              formatter: '{value} %',
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series,
      };

      myChart.setOption(option);

      // 监听图例选择事件，实现旋转选择逻辑
      myChart.on('legendselectchanged', (params: any) => {
        const clickedSeries = params.name;
        const isCurrentlySelected = params.selected[clickedSeries];

        if (isCurrentlySelected) {
          // 如果点击的是要选中的系列
          if (selectedSeriesRef.current.includes(clickedSeries)) {
            // 如果已经在选中列表中，不做处理
            return;
          }

          // 如果选中列表已满6个，移除第一个
          if (selectedSeriesRef.current.length >= 6) {
            selectedSeriesRef.current.shift();
          }

          // 添加新选中的系列
          selectedSeriesRef.current.push(clickedSeries);
        } else {
          // 如果点击的是要取消选中的系列
          selectedSeriesRef.current = selectedSeriesRef.current.filter(name => name !== clickedSeries);
        }

        // 更新图例选中状态
        const newSelected: Record<string, boolean> = {};
        seriesNames.forEach(seriesName => {
          newSelected[seriesName] = selectedSeriesRef.current.includes(seriesName);
        });

        myChart.setOption({
          legend: {
            selected: newSelected
          }
        });

        // 通知父组件选中状态变化
        if (onSelectedSeriesChange) {
          onSelectedSeriesChange([...selectedSeriesRef.current]);
        }
      });

      const resizeHandler = () => myChart.resize();
      window.addEventListener('resize', resizeHandler);

      return () => {
        window.removeEventListener('resize', resizeHandler);
        myChart.dispose();
      };
    }
  }, [currency, dates, series, onSelectedSeriesChange, currencyFormattedSeries, percentageFormattedSeries]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

export default ReusableChart;
