import React, { useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Card, Button, message } from 'antd';
import { sourceImageUrl } from '@/utils/common';
import styles from './index.less';
import { useModel, history } from '@umijs/max';
import { guestLogin } from '@/services/ibidder_api/user';
import { AuthUtils } from '@/utils/auth';

const NoShop: React.FC = () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const { permissions } = initialState || {};
  const canViewShopAuth = permissions?.shop_auth_perm === 'MANAGE';
  const hasShop = permissions?.has_shop;

  useEffect(() => {
    if (canViewShopAuth) {
      history.push('/authorization/shop-authorization');
    }
  }, [canViewShopAuth, hasShop]);

  const handleGuestLogin = async () => {
    try {
      const res = await guestLogin() as any;
      if (res.code === 200) {
        message.success('登录成功！');
        AuthUtils.handleLoginSuccess(res.data);
        localStorage.setItem('isGuestMode', 'true');
        sessionStorage.removeItem('guestTipClosed');
        // re-fetch user info
        const storedUserInfo = AuthUtils.getUserInfo();
        if (storedUserInfo) {
          const userInfo = {
            name: AuthUtils.getUserDisplayName(),
            avatar: storedUserInfo.avatar || sourceImageUrl('default_avatar.png'),
            userid: storedUserInfo.id.toString(),
            email: storedUserInfo.email || '',
            phone: storedUserInfo.phone || '',
            user_type: storedUserInfo.user_type || 0,
            access: 'admin',
          };
          setInitialState((s) => ({
            ...s,
            currentUser: userInfo,
          }));
        }
        window.location.href = '/admin/listing';
      } else {
        message.error(res.message);
      }
    } catch (error) {
      console.error('登录失败，请重试！', error);
    }
  };


  return (
    <div className={styles.noShop}>
      <PageContainer title={false} breadcrumbRender={false}>
        <Card style={{border: 'none', background: 'transparent'}}>
          <div style={{ width: '100%', maxWidth: 1280, height: 'calc(100vh - 120px)', maxHeight: 666, display: 'flex', alignItems: 'center', background: '#fff', margin: '-24px auto' }}>
            <img src={sourceImageUrl('no_auth.svg')} alt="No Auth" style={{ height: "100%" }} />
            <div style={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', gap: 24 }}>
              <div style={{ fontSize: 28, color: '#1D2129', textAlign: 'center', lineHeight: 1.4 }}>欢迎来到FlyPower</div>
              <div style={{ fontSize: 16, color: '#1D2129', textAlign: 'center' }}>请先联系您的上级完成账号权限分配，<br />在此之前，您可点击下方按钮进入游客体验模式</div>
              <Button type="primary" onClick={handleGuestLogin} style={{ width: 370, height: 40, fontSize: 16, borderRadius: 20 }}>Demo体验</Button>
            </div>
          </div>
        </Card>
      </PageContainer>
    </div>
  );
};

export default NoShop;
