import type { Mapping } from './dataTransformer.types';

// 为 Real_ads_result 创建映射配置
export const realAdsResultConfig: { [K in keyof Strategy.Real_ads_result]: Mapping } = {
  acos: { type: 'primitive', path: 'acos', defaultValue: '' },
  clicks: { type: 'primitive', path: 'clicks', defaultValue: '' },
  cvr: { type: 'primitive', path: 'cvr', defaultValue: '' },
  impressions: { type: 'primitive', path: 'impressions', defaultValue: '' },
  orders: { type: 'primitive', path: 'orders', defaultValue: '' },
  sales: { type: 'primitive', path: 'sales', defaultValue: '' },
  spend: { type: 'primitive', path: 'spend', defaultValue: '' }
};
