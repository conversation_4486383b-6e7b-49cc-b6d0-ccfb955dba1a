# 本地运行 dist 目录里面的文件，http://localhost:8008/
# 安装 caddy（brew install caddy） 后，在项目根目录下创建 Caddyfile 文件，内容如下：
# 运行 caddy run 启动代理服务
:8008 {
    # 反向代理 /api/ 的请求到后端服务器
    # `*` 是通配符，表示匹配所有路径。
    reverse_proxy /api/* http://***************:8000 {
        # 重写 Host 请求头，让后端服务器认为请求来自它自己
        header_up Host {http.reverse_proxy.upstream.host}
        # 移除 Origin 请求头，避免 CORS 限制
        header_up -Origin
    }

    # 提供打包后的静态文件服务
    # `root` 指定静态文件的根目录，这里是项目根目录下的 dist 文件夹
    root * ./dist
    # `file_server` 启用静态文件服务器
    file_server

    # 重写所有未找到的路径到 index.html，用于单页应用的路由
    handle_errors {
        rewrite * /index.html
        file_server
    }
}