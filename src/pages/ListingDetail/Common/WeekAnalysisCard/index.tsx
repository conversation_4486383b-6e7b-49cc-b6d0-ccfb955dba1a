import React from 'react';
import { Card, Row, Col, Typography, Flex } from 'antd';
import { approachDay, approachDayColor } from '@/utils/bus';
const { Title, Text } = Typography;

const WeekAnalysisCard: React.FC<{ ads_suggestion: Strategy.WeekStrategyData['ads_suggestion'] }> = (props) => {
  const { ads_suggestion } = props;
  return (
    <Row gutter={16} align="stretch" className='card-row'>
      <Col span={8}>
        <Card data-test-id="week-analysis-card-overall-strategy" className="card">
          <Flex justify="space-between" align="center">
            <Title level={4} style={{ marginBottom: 0 }}>总体策略</Title>
          </Flex>
          <div style={{ flex: '1 0 auto', display: 'flex', flexDirection: 'column', justifyContent: 'center', marginBottom: 8, marginTop: 8 }}>
            <div style={{ fontSize: '2em', fontWeight: 'bold', color: approachDayColor(ads_suggestion.approach) }}>
              {approachDay(ads_suggestion.approach)}
            </div>
          </div>
          <div style={{ marginTop: 'auto' }}>
            <Text style={{ fontSize: '14px', color: '#666' }}>{ads_suggestion.rationale}</Text>
          </div>
        </Card>
      </Col>
      <Col span={8}>
        <Card data-test-id="week-analysis-card-primary-goal" className='card'>
          <Flex justify="space-between" align="center">
            <Title level={4} style={{ marginBottom: 0 }}>主要目标</Title>
          </Flex>
          <div style={{ flex: '1 0 auto', display: 'flex', flexDirection: 'column', justifyContent: 'center', marginBottom: 8, marginTop: 8 }}>
            <div style={{ fontSize: '18px', fontWeight: 'bold' }}>{ads_suggestion.primary_goal.goal}</div>
          </div>
          <div style={{ marginTop: 'auto' }}>
            <Text style={{ fontSize: '14px', color: '#666' }}>{ads_suggestion.primary_goal.rationale}</Text>
          </div>
        </Card>
      </Col>
      <Col span={8}>
        <Card data-test-id="week-analysis-card-other-goals" className='card'>
          <Flex justify="space-between" align="center">
            <Title level={4} style={{ marginBottom: 0 }}>其他目标</Title>
          </Flex>
          <div style={{ flex: '1 0 auto', display: 'flex', flexDirection: 'column', justifyContent: 'center', marginBottom: 8, marginTop: 8 }}>
            <div style={{ height: '100%' }}>
              <ul className="goalList">
                {ads_suggestion?.other_goals?.map((goal, index) => (
                  <li key={index} className="goalItem" style={{ marginBottom: index < ads_suggestion?.other_goals.length - 1 ? 12 : 0 }}>
                    <div className="goalDot"></div>
                    <Text style={{ textAlign: 'left', lineHeight: '1.5' }}>{goal}</Text>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </Card>
      </Col>
    </Row>
  )
}

export default WeekAnalysisCard;