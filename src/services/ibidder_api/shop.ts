import { request } from '@/request';

export interface ShopItem {
  shop_name: string;
  profile_id: string;
  country_code: string;
  shop_status: boolean;
  ads_status: boolean;
  status: 'enable' | 'disable' | 'abnormal';
  sync_status: boolean;
  update_time: string;
  authorized_name: string;
}

export interface ShopFilterOptions {
  market: string[];
  shop: {
    name: string;
    id: string;
  }[];
  seller_id: string[];
  country_code: string[];
}

// 获取授权链接
export async function getAuthLink(data: {
  market: string;
  country_code_list: string[];
  shop_name: string;
}) {
  return request<{
    code: number;
    data: { url: string };
    message: string;
  }>('/api/v1/ads/profile/get_auth_url', {
    method: 'POST',
    data,
  });
}

export interface ShopListParams {
  page?: number;
  page_size?: number;
  market?: string;
  profile_id?: string;
  seller_id?: string;
  country_code?: string;
  shop_status?: boolean;
  ads_status?: boolean;
  shop_name?: string;
}

export interface ShopListResponse {
  list: ShopItem[];
  total: number;
  has_shop: boolean;
}

// 获取店铺列表
export async function getShopsList(data?: ShopListParams) {
  return request<{
    code: number;
    data: ShopListResponse;
    message: string;
  }>('/api/v1/ads/profile/list', {
    method: 'POST',
    data,
  });
}

// 获取授权结果
export async function getAuthResult(params: {
  state: string;
}) {
  return request<{
    code: number;
    data: {
      status: string;
      message: string;
    };
    message: string;
  }>('/api/v1/ads/profile/get_auth_result', {
    method: 'GET',
    params,
  });
}

// 获取店铺筛选选项
export async function getShopFilterOptions() {
  return request<{
    code: number;
    data: ShopFilterOptions;
    message: string;
  }>('/api/v1/ads/profile/filter_options', {
    method: 'GET',
  });
}

// 更新店铺
export async function updateShop(data: {
  profile_id: string;
  shop_name?: string;
  ads_status?: boolean;
  sync_status?: boolean;
  status?: 'enabled' | 'disabled';
  is_delete?: boolean;
}) {
  return request<{
    code: number;
    data: any;
    message: string;
  }>('/api/v1/ads/profile/update_profile', {
    method: 'POST',
    data,
  });
}

// 同步站点
export async function syncProfiles(data: {
  market: string;
  seller_id: string;
}) {
  return request<{
    code: number;
    data: any;
    message: string;
  }>('/api/v1/ads/profile/sync_profiles', {
    method: 'POST',
    data,
  });
}