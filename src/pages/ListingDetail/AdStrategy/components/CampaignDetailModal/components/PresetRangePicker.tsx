import React from 'react';
import { DatePicker, Tooltip } from 'antd';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { useModel } from '@umijs/max';
import { countryTimezoneMap } from '@/utils/bus';
import { QuestionCircleOutlined } from '@ant-design/icons';

const { RangePicker } = DatePicker;

interface PresetRangePickerProps {
    value?: [Dayjs, Dayjs];
    onChange?: (dates: null | (Dayjs | null)[], dateStrings: [string, string]) => void;
    style?: React.CSSProperties;
}

const PresetRangePicker: React.FC<PresetRangePickerProps> = ({ value, onChange, style }) => {
    const { productInfo } = useModel('productInfo');
    const country = (productInfo?.country || '').toLowerCase();
    const tz = countryTimezoneMap[country as keyof typeof countryTimezoneMap];
    const now = tz ? dayjs().tz(tz) : dayjs();

    const rangePresets = [
        {
            label: <span>昨天 <Tooltip title="此时间为站点时间今天。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(1, 'day'), now.clone().subtract(1, 'day')] as [Dayjs, Dayjs],
        },
        {
            label: <span>最近3天 <Tooltip title="此时间为站点的最近3天。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(2, 'day'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>最近7天 <Tooltip title="此时间为站点的最近7天。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(6, 'day'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>本周 <Tooltip title="此时间为站点时间本周。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().startOf('week'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>上周 <Tooltip title="此时间为站点时间上周。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [
                now.clone().subtract(1, 'week').startOf('week'),
                now.clone().subtract(1, 'week').endOf('week')
            ] as [Dayjs, Dayjs],
        },
        {
            label: <span>最近14天 <Tooltip title="此时间为站点时间最近14天。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(13, 'day'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>最近30天 <Tooltip title="此时间为站点时间最近30天。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(29, 'day'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>最近60天 <Tooltip title="此时间为站点时间最近60天。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(59, 'day'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>最近90天 <Tooltip title="此时间为站点时间最近90天。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(89, 'day'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>本月 <Tooltip title="此时间为站点时间本月。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().startOf('month'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>上个月 <Tooltip title="此时间为站点时间上个月。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [
                now.clone().subtract(1, 'month').startOf('month'),
                now.clone().subtract(1, 'month').endOf('month')
            ] as [Dayjs, Dayjs],
        },
        {
            label: <span>本年至今 <Tooltip title="此时间为站点时间本年至今。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().startOf('year'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>最近一年 <Tooltip title="此时间为站点时间最近一年。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(364, 'day'), now.clone()] as [Dayjs, Dayjs],
        },
        {
            label: <span>最近半年 <Tooltip title="此时间为站点时间最近半年。"><QuestionCircleOutlined /></Tooltip></span>,
            value: [now.clone().subtract(6, 'month'), now.clone()] as [Dayjs, Dayjs],
        },
    ];

    const disabledDate = (current: Dayjs) => {
        const endOfToday = dayjs().endOf('day');
        return current && current > endOfToday;
    };

    return (
        <RangePicker
            value={value}
            onChange={onChange}
            presets={rangePresets}
            style={style}
            disabledDate={disabledDate}
        />
    );
};

export default PresetRangePicker;