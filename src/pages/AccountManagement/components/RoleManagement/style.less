.roleManagement {
  margin-top: -16px;
  .roleListContainer {
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    height: 100%;
    .roleListHeader {
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 8px;
      font-weight: 500;
      border-bottom: 1px solid #f0f0f0;
    }
    .roleList {
      flex: 1;
      overflow-y: auto;
    }
      
    .roleItem {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 12px 16px;
      cursor: pointer;
      &.active {
        background-color: #f7f7f7;
      }
      &:last-child {
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }

  .permissionContainer {
    
    .permissionHeader {
      height: 60px;
      display: flex;
      align-items: center;
      padding: 0 8px;
      font-weight: 500;
      border-bottom: 1px solid #f0f0f0;
    }


    .permissionRow {
      padding: 12px 8px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }
    }

    .permissionCategory {
      font-weight: 500;
    }
    
    .permissionFooter {
      padding: 16px;
      text-align: right;
      border-top: 1px solid #f0f0f0;
    }
  }
}
