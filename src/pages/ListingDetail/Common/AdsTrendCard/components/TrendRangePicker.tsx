import React, { useMemo } from 'react';
import { DatePicker, Tooltip } from 'antd';
import dayjs, { type Dayjs } from 'dayjs';
import { useModel } from '@umijs/max';
import { countryTimezoneMap } from '@/utils/bus';
import { QuestionCircleOutlined } from '@ant-design/icons';

const { RangePicker } = DatePicker;

interface TrendRangePickerProps {
  type: 'day' | 'week' | 'month';
  value: [Dayjs, Dayjs];
  onChange: (dates: null | (Dayjs | null)[]) => void;
}

const TrendRangePicker: React.FC<TrendRangePickerProps> = ({
  type,
  value,
  onChange,
}) => {
  const { productInfo } = useModel('productInfo');
  const country = (productInfo?.country || '').toLowerCase();
  const tz = countryTimezoneMap[country as keyof typeof countryTimezoneMap];
  const now = tz ? dayjs().tz(tz) : dayjs();

  const presets = useMemo(() => {
    if (type === 'day') {
      const today = now.clone();
      const yesterday = today.subtract(1, 'day');
      const startOfWeek = today.startOf('week');
      const startOfLastWeek = today.subtract(1, 'week').startOf('week');
      const endOfLastWeek = startOfLastWeek.endOf('week');
      const startOfMonth = today.startOf('month');
      const startOfLastMonth = today.subtract(1, 'month').startOf('month');
      const endOfLastMonth = startOfLastMonth.endOf('month');
      const startOfYear = today.startOf('year');

      const opts: { label: React.ReactNode, value: [Dayjs, Dayjs] }[] = [
        {
          label: <span>昨天 <Tooltip title="此时间为站点时间昨天。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [yesterday, yesterday]
        },
        {
          label: <span>最近3天 <Tooltip title="此时间为站点的最近3天。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(2, 'day'), today]
        },
        {
          label: <span>最近7天 <Tooltip title="此时间为站点的最近7天。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(6, 'day'), today]
        },
        {
          label: <span>本周 <Tooltip title="此时间为站点时间本周。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [startOfWeek, today]
        },
        {
          label: <span>上周 <Tooltip title="此时间为站点时间上周。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [startOfLastWeek, endOfLastWeek]
        },
        {
          label: <span>最近14天 <Tooltip title="此时间为站点时间最近14天。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(13, 'day'), today]
        },
        {
          label: <span>最近30天 <Tooltip title="此时间为站点时间最近30天。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(29, 'day'), today]
        },
        {
          label: <span>最近60天 <Tooltip title="此时间为站点时间最近60天。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(59, 'day'), today]
        },
        {
          label: <span>最近90天 <Tooltip title="此时间为站点时间最近90天。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(89, 'day'), today]
        },
        {
          label: <span>最近半年 <Tooltip title="此时间为站点时间最近半年。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(6, 'month'), today]
        },
        {
          label: <span>最近一年 <Tooltip title="此时间为站点时间最近一年。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(364, 'day'), today]
        },
        {
          label: <span>本年至今 <Tooltip title="此时间为站点时间本年至今。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [startOfYear, today]
        },
        {
          label: <span>本月 <Tooltip title="此时间为站点时间本月。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [startOfMonth, today]
        },
        {
          label: <span>上个月 <Tooltip title="此时间为站点时间上个月。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [startOfLastMonth, endOfLastMonth]
        },
      ];
      return opts;
    }
    if (type === 'week') {
      const today = now.clone();
      const startOfWeek = today.startOf('week');
      const startOfLastWeek = today.subtract(1, 'week').startOf('week');
      const endOfLastWeek = startOfLastWeek.endOf('week');

      const opts: { label: React.ReactNode, value: [Dayjs, Dayjs] }[] = [
        {
          label: <span>本周 <Tooltip title="此时间为站点时间本周。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [startOfWeek, today]
        },
        {
          label: <span>上周 <Tooltip title="此时间为站点时间上周。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [startOfLastWeek, endOfLastWeek]
        },
        {
          label: <span>最近14周 <Tooltip title="此时间为站点时间最近14周。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(13, 'week'), today]
        },
        {
          label: <span>最近半年 <Tooltip title="此时间为站点时间最近半年。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(6, 'month'), today]
        },
      ];
      return opts;
    }
    if (type === 'month') {
      const today = now.clone();
      const startOfYear = today.startOf('year');

      const opts: { label: React.ReactNode, value: [Dayjs, Dayjs] }[] = [
        {
          label: <span>最近3个月 <Tooltip title="此时间为站点时间最近3个月。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(3, 'month'), today]
        },
        {
          label: <span>最近6个月 <Tooltip title="此时间为站点时间最近6个月。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(6, 'month'), today]
        },
        {
          label: <span>最近12个月 <Tooltip title="此时间为站点时间最近12个月。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [today.subtract(12, 'month'), today]
        },
        {
          label: <span>本年至今 <Tooltip title="此时间为站点时间本年至今。"><QuestionCircleOutlined /></Tooltip></span>,
          value: [startOfYear, today]
        },
      ];
      return opts;
    }
    return [];
  }, [type, now]);

  return (
    <RangePicker
      allowClear={false}
      value={value}
      onChange={onChange}
      presets={presets as any}
      picker={type === 'day' ? 'date' : type}
      popupClassName={type === 'week' ? 'weekPickerPopup' : ''}
      disabledDate={(current) => {
        return current && current > dayjs().endOf('day');
      }}
    />
  );
};

export default TrendRangePicker;