import React, { useState, useEffect, useRef } from 'react';
import { getAdStrategy, reportUpdateStatus } from '@/services/ibidder_api/operation';
import { Alert, Badge, Card, message } from 'antd';
import { useRequest, useSearchParams, useModel, history } from '@umijs/max';
import { PageContainer } from '@ant-design/pro-layout';
import { ProCard } from '@ant-design/pro-components';
import {
  Image,
  Typography,
  Rate,
  Space,
  Flex,
  Button,
  Spin,
} from 'antd';
import AdStrategy from './AdStrategy';
import ListingSelector from './Common/ListingSelector';
import ConfirmAdStrategyAlert from './Common/ConfirmAdStrategyAlert';
import type { TabsProps } from 'antd';
import 'react-resizable/css/styles.css';

import { getListingInfo } from '@/services/ibidder_api/listings';
import AiWork from './AiWork';
import AiSetting from './AiSetting';
import CampaignManage from './CampaignManage';
import ListAdsTarget from './Common/ListAdsTarget';
import { getCountryTimezone } from '@/utils/bus';
import { getSiteCurrency } from '@/utils/common';

// 导入弹框组件

import {
  DayStrategyModal,
  WeekStrategyModal,
  WeekReportModal,
  MonthReportModal,
  DayStrategyCatModal
} from './Common/Modals';
import { AmazonOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const ListingDetail: React.FC = () => {
  const { triggerRefresh } = useModel('strategyRefresh');
  // 搜索参数
  const [searchParams, setSearchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const { productInfo, setProductInfoFn } = useModel('productInfo');
  const country = productInfo?.country;

  // 全局状态管理
  const { updateAlert, updateAlertFn } = useModel('updateAlert');
  const { unreadCount } = useModel('unreadCount');
  const { updateProductInfo, updateProductInfoFn } = useModel('updateProductInfo');

  // 全局弹框状态管理
  const {
    dayStrategyModal,
    closeDayStrategyModal,
    weekStrategyModal,
    closeWeekStrategyModal,
    weekReportModal,
    closeWeekReportModal,
    monthReportModal,
    closeMonthReportModal,
    dayStrategyCatModal,
    closeDayStrategyCatModal
  } = useModel('globalModals');

  // Modal 相关状态
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [pollingStatus, setPollingStatus] = useState<Array<{ type: 'info' | 'success' | 'warning'; message: string; job_id: string; es_id: string }>>([]);

  // 轮询控制相关状态
  const pollingControlRef = useRef<{
    timeoutId: NodeJS.Timeout | null;
    currentCount: number;
    maxCount: number;
    isPolling: boolean;
  }>({
    timeoutId: null,
    currentCount: 0,
    maxCount: 10,
    isPolling: false,
  });

  // 修改数据获取和使用方式
  const { loading, run: refreshProductInfo, error } = useRequest(() => getListingInfo({ parent_asin: asin, profile_id: profile_id }), {
    ready: !!asin,
    manual: false,
    refreshDeps: [asin],
    onSuccess: (response: API.ListingInfo | { data: API.ListingInfo }) => {
      const result = (response as { data: API.ListingInfo }).data || response;
      const country = result?.country || 'US';
      const currency = getSiteCurrency(country);
      result.currency = currency;
      setProductInfoFn(result);
    },
    onError: (error) => {
      console.error(error);
      history.push('/admin/listing');
    }
  });

  // 添加打开 Modal 的处理函数
  const showModal = () => {
    setIsModalOpen(true);
  };

  // 添加选择商品的处理函数
  const reportUpdateStatusData = async (params: { doc_id_list: string[] }, isInitialCall: boolean = false) => {
    // 如果是初始调用，重置计数器并取消之前的轮询
    if (isInitialCall) {
      if (pollingControlRef.current.timeoutId) {
        clearTimeout(pollingControlRef.current.timeoutId);
        pollingControlRef.current.timeoutId = null;
      }
      pollingControlRef.current.currentCount = 0;
      pollingControlRef.current.isPolling = true;
    }

    if (!params.doc_id_list || params.doc_id_list.length === 0) {
      setPollingStatus([]);
      pollingControlRef.current.isPolling = false;
      return;
    }

    // 检查是否超过最大执行次数
    if (pollingControlRef.current.currentCount >= pollingControlRef.current.maxCount) {
      console.log('已达到最大轮询次数，停止轮询');
      setPollingStatus([]);
      pollingControlRef.current.isPolling = false;
      return;
    }

    // 增加执行计数
    pollingControlRef.current.currentCount++;
    console.log(`第 ${pollingControlRef.current.currentCount} 次执行 reportUpdateStatusData`);

    try {
      const res = await reportUpdateStatus({
        doc_id_list: params.doc_id_list,
      });

      if (res.data && res.code === 200) {
        const data = res.data;
        if (!data || data.length === 0) {
          setPollingStatus([]);
          pollingControlRef.current.isPolling = false;
          return;
        }

        // 获取job_id对应的中文提示信息
        const getJobMessage = (jobId: string) => {
          const jobMessages: Record<string, string> = {
            'ads_strategy_week': '周广告策略正在制定中...',
            'ads_strategy_day': '日广告策略正在制定中...',
            'market_report_month': '月度市场分析报告正在制定中...',
          };
          return jobMessages[jobId] || '策略正在制定中...';
        };

        // 处理成功的项目 - 显示成功提示
        const currentlyPollingEsIds = pollingStatus.map(item => item.es_id);
        const successItems = data.filter((item: any) => item.is_create_success && currentlyPollingEsIds.includes(item.es_id));
        successItems.forEach((item: any) => {
          triggerRefresh();
          const getSuccessMessage = (jobId: string) => {
            const successMessages: Record<string, string> = {
              'ads_strategy_week': '周广告策略制定完成',
              'ads_strategy_day': '日广告策略制定完成',
              'market_report_month': '月度市场分析报告制定完成',
            };
            return successMessages[jobId] || '策略制定完成';
          };

          message.open({
            type: 'success',
            content: getSuccessMessage(item.job_id),
            duration: 3
          });
        });

        // 筛选出is_create_success为false的项目
        const failedItems = data.filter((item: any) => !item.is_create_success);

        if (failedItems.length > 0 && pollingControlRef.current.currentCount < pollingControlRef.current.maxCount) {
          // 设置Alert状态
          const alertItems = failedItems.map((item: any) => ({
            type: 'info' as const,
            message: getJobMessage(item.job_id),
            job_id: item.job_id,
            es_id: item.es_id
          }));
          setPollingStatus(alertItems);

          // 继续轮询
          const failedIds = failedItems.map((item: any) => item.es_id);
          pollingControlRef.current.timeoutId = setTimeout(() => {
            reportUpdateStatusData({ doc_id_list: failedIds }, false);
          }, 30_000);
        } else {
          setPollingStatus([]);
          pollingControlRef.current.isPolling = false;
        }
      } else {
        message.error(res.message || '报告状态更新失败');
        setPollingStatus([]);
        pollingControlRef.current.isPolling = false;
      }
    } catch (error) {
      setPollingStatus([]);
      pollingControlRef.current.isPolling = false;
    }
  };

  const handleDocIdListUpdate = (updatedDocIdList: string[]) => {
    reportUpdateStatusData({ doc_id_list: updatedDocIdList }, true);
  };

  const refreshStrategyData = async () => {
    if (country) {
      const results = await Promise.all([
        getAdStrategy({
          date: getCountryTimezone(country, 'YYYY-MM-DD'),
          job_id: 'market_report_month',
          asin,
          profile_id,
        }),
        getAdStrategy({
          date: getCountryTimezone(country, 'YYYY-MM-DD'),
          job_id: 'ads_strategy_week',
          asin,
          profile_id,
        }),
        getAdStrategy({
          date: getCountryTimezone(country, 'YYYY-MM-DD'),
          job_id: 'ads_strategy_day',
          asin,
          profile_id,
        }),
      ]);
      const [monthRes, weekRes, dayRes] = results as [any, any, any];
      let monthData = null;
      let weekData = null;
      let dayData = null;

      if (monthRes.code === 200) {
        monthData = monthRes.data;
      }
      if (weekRes.code === 200) {
        weekData = weekRes.data;
      }
      if (dayRes.code === 200) {
        dayData = dayRes.data;
      }
      handleDocIdListUpdate([monthData?.es_id, weekData?.es_id, dayData?.es_id].filter(Boolean) as string[]);
    }
  }

  // 监听updateAlert状态变化
  useEffect(() => {
    if (updateAlert) {
      // 重置状态
      setTimeout(() => {
        updateAlertFn(false);
      }, 1000);
      // 这里可以添加其他需要执行的逻辑
      refreshStrategyData()
    }
  }, [updateAlert, updateAlertFn]);

  // 监听updateProductInfo状态变化
  useEffect(() => {
    if (updateProductInfo) {
      updateProductInfoFn(false); // 重置状态
      refreshProductInfo(); // 刷新产品信息
    }
  }, [updateProductInfo, updateProductInfoFn, refreshProductInfo]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (pollingControlRef.current.timeoutId) {
        clearTimeout(pollingControlRef.current.timeoutId);
      }
    };
  }, []);

  // 添加选择商品的处理函数
  const handleSelectListing = (asin: string, profile_id: string) => {
    setSearchParams({ asin, profile_id });
    setIsModalOpen(false);
  };

  if (error?.name === 'APIError') {
    return <div style={{ marginTop: 30, textAlign: 'center' }}>{error?.message}</div>
  }

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'adStrategy',
      label: '策略看板',
      children: (
        <>
          <AdStrategy onDocIdListUpdate={handleDocIdListUpdate} />
        </>
      ),
    },
    {
      key: 'aiWork',
      label: <>工作台{unreadCount > 0 ? <Badge count={unreadCount} style={{ marginLeft: 8 }} /> : ''}</>,
      children: (
        <>
          <AiWork />
        </>
      ),
    },
    {
      key: 'AiSetting',
      label: '设置',
      children: (
        <>
          <AiSetting />
        </>
      ),
    },
    {
      key: 'campaignManage',
      label: 'Campaign管理',
      children: (
        <>
          <CampaignManage />
        </>
      ),
    },
  ];

  if (!productInfo) {
    return <Spin style={{ marginTop: '15%' }} spinning={true} />
  }

  return (
    <PageContainer title={false}>
      {/* 顶部商品信息区 */}
      <Card
        data-test-id="listingDetail-productInfo"
        className='card'
        style={{ marginBottom: 16 }}
        loading={loading}
      >
        <Flex gap={20}>
          {/* 左侧商品图片 */}
          <div style={{ width: 160 }}>
            <Image
              width={160}
              height={160}
              src={productInfo?.image_url}
              alt="商品图片"
              preview={true}
            />
          </div>

          {/* 中间商品信息 */}
          <Flex vertical style={{ flex: 1, height: 160 }} justify="space-between">
            <Title level={4} ellipsis={{ rows: 3 }} style={{ marginTop: 0, marginBottom: 0 }}>
              {productInfo?.title}
            </Title>

            <Flex justify="space-between" align="center">
              <Space direction="horizontal" size="middle" style={{ marginBottom: 8 }}>
                <Space>
                  <Text strong>ASIN: {productInfo?.asin}</Text>
                  <a href={productInfo.url} target="_blank" rel="noreferrer" className='amazonIcon'>
                    <AmazonOutlined style={{ width: 12, height: 12 }} />
                  </a>
                </Space>
                <Text strong>价格: {productInfo?.currency || '$'}{productInfo?.price}</Text>
                <Space>
                  <Rate allowHalf value={productInfo?.rating} disabled />
                  <Text strong>{productInfo?.rating} ({productInfo?.review_count}条评价)</Text>
                </Space>
              </Space>
              <Button type="primary" onClick={showModal}>切换商品</Button>
            </Flex>
          </Flex>
        </Flex>
      </Card>

      {
        productInfo.report_status === true ?
          <>
            <div style={{ marginBottom: 16 }}>
              {productInfo && <ListAdsTarget data={productInfo} />}
            </div>
            {pollingStatus.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                {pollingStatus.map((status, index) => (
                  <Alert
                    key={`${status.es_id}-${index}`}
                    message={status.message}
                    type={status.type}
                    showIcon
                    closable
                    onClose={() => {
                      setPollingStatus(prev => prev.filter((_, i) => i !== index));
                    }}
                    style={{ marginBottom: index < pollingStatus.length - 1 ? 8 : 0 }}
                  />
                ))}
              </div>
            )}

            <ConfirmAdStrategyAlert />

            {/* 底部表格区 */}
            <ProCard
              className='card noHover'
              data-test-id="listingDetail-tabs"
              tabs={{
                destroyInactiveTabPane: true,
                items: tabItems,
                tabPosition: 'top',
                tabBarGutter: 32,
                tabBarStyle: {
                  marginBottom: 0,
                  paddingLeft: 16,
                  paddingRight: 16,
                },
                tabBarExtraContent: {
                  right: null,
                },
              }}
              bodyStyle={{ padding: 0 }}
            >
            </ProCard>
          </>
          :
          <div>
            <Alert
              description="商品不在listing管理列表中，请先添加该商品"
              type="warning"
              showIcon
            />
          </div>
      }

      {/* 添加 Modal 组件 */}
      <ListingSelector
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onSelect={handleSelectListing}
      />

      {/* 使用新的高阶弹框组件 */}
      {/* 日策略弹框 */}
      <DayStrategyModal
        open={dayStrategyModal.open}
        onCancel={closeDayStrategyModal}
        job_id={dayStrategyModal.data?.job_id || ''}
        current_time={dayStrategyModal.data?.current_time || ''}
        target_job_id={dayStrategyModal.data?.target_job_id}
        date={dayStrategyModal.data?.date}
        onSuccess={() => { }}
      />

      {/* 周策略弹框 */}
      <WeekStrategyModal
        open={weekStrategyModal.open}
        onCancel={closeWeekStrategyModal}
        job_id={weekStrategyModal.data?.job_id || ''}
        current_time={weekStrategyModal.data?.current_time || ''}
        target_job_id={weekStrategyModal.data?.target_job_id}
        date={weekStrategyModal.data?.date}
        isCompleteStrategy={weekStrategyModal.data?.isCompleteStrategy}
        onSuccess={() => { }}
      />

      {/* 周报告弹框 */}
      <WeekReportModal
        open={weekReportModal.open}
        onCancel={closeWeekReportModal}
        job_id={weekReportModal.data?.job_id || ''}
        current_time={weekReportModal.data?.current_time || ''}
        type="week"
        onSuccess={() => { }}
      />

      {/* 月报告弹框 */}
      <MonthReportModal
        open={monthReportModal.open}
        onCancel={closeMonthReportModal}
        job_id={monthReportModal.data?.job_id || ''}
        current_time={monthReportModal.data?.current_time || ''}
        target_job_id={monthReportModal.data?.target_job_id}
        type="month"
        onSuccess={() => { }}
      />

      {/* 日策略分类弹框 */}
      <DayStrategyCatModal
        open={dayStrategyCatModal.open}
        onCancel={closeDayStrategyCatModal}
        job_id={dayStrategyCatModal.data?.job_id || ''}
        current_time={dayStrategyCatModal.data?.current_time || ''}
        target_job_id={dayStrategyCatModal.data?.target_job_id}
        onSuccess={() => { }}
      />
    </PageContainer>
  );
};

export default ListingDetail;
