export const marketTranslation: { [key: string]: string } = {
  'NA': '北美',
  'EU': '欧洲',
  'FE': '亚太',
};

export const countryTranslation: { [key: string]: string } = {
  'US': '美国',
  'CA': '加拿大',
  'MX': '墨西哥',
  'BR': '巴西',
  'UK': '英国',
  'DE': '德国',
  'FR': '法国',
  'IT': '意大利',
  'ES': '西班牙',
  'NL': '荷兰',
  'SE': '瑞典',
  'PL': '波兰',
  'BE': '比利时',
  'SA': '沙特',
  'AE': '阿联酋',
  'EG': '埃及',
  'TR': '土耳其',
  'IN': '印度站',
  'JP': '日本站',
  'AU': '澳大利亚',
  'SG': '新加坡',
};

export const marketName = (market: string) => {
  const marketStr = market?.toUpperCase() || '-';
  return marketTranslation[marketStr as keyof typeof marketTranslation] || '';
};

export const countryName = (country: string) => {
  const countryStr = country?.toUpperCase() || '-';
  return countryTranslation[countryStr as keyof typeof countryTranslation] || '';
};
