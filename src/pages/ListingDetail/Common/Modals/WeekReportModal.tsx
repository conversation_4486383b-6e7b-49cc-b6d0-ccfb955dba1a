import MonthAnalysisContent from '../../AdStrategy/components/monthAnalysis';
import withModalWrapper, { ModalWrapperProps, ContentComponentProps } from '../ModalWrapper';
import { WeekMonthReportModalData } from '@/models/globalModals';

// 组合props类型
type WeekReportModalProps = Omit<WeekMonthReportModalData, keyof ContentComponentProps> & 
  ModalWrapperProps & 
  ContentComponentProps & {
    type: 'week';
  };

// 使用高阶组件包装内容组件
const WeekReportModal = withModalWrapper(MonthAnalysisContent);

export default WeekReportModal;
export type { WeekReportModalProps }; 