/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(initialState: {
  permissions?: API.Permissions | undefined;
  currentUser?: any;
}) {
  const { permissions, currentUser } = initialState ?? {};

  return {
    // 新增的权限控制
    canViewShopAuth: permissions && permissions.shop_auth_perm === 'MANAGE',
    canManageSubAccounts: permissions?.account_management_perm === 'MANAGE',
    canManageGroups: permissions?.group_management_perm === 'MANAGE',
    canManageRoles: permissions?.role_management_perm === 'MANAGE',
    canViewOperationLog: permissions?.operation_log_perm !== 'NO_PERMISSION',
    canViewAuthorization: permissions?.shop_auth_perm === 'MANAGE' 
    || permissions?.account_management_perm === 'MANAGE' 
    // || permissions?.group_management_perm === 'MANAGE' 
    || permissions?.role_management_perm === 'MANAGE' 
    || permissions?.operation_log_perm !== 'NO_PERMISSION',
    canViewTeamManagement: permissions?.account_management_perm === 'MANAGE' ||
      // permissions?.group_management_perm === 'MANAGE' ||
      permissions?.role_management_perm === 'MANAGE' ||
      permissions?.operation_log_perm !== 'NO_PERMISSION',
    hasShop: permissions?.has_shop,

    // 兼容之前的权限控制
    // 之前的逻辑让所有登录用户都拥有 canAdmin 权限
    canAdmin: !!currentUser,
    // 之前的逻辑中没有 'user' 角色，所以 normalUser 始终为 false
    normalUser: false,
  };
};
