import React from 'react';
import ReactECharts from 'echarts-for-react';
import { colorPrimary, getCountryTimezone } from '@/utils/bus';

interface IMonthlyTrendsProps {
  chartData: Strategy.MonthlyTrendItem[]
}

import { useModel } from 'umi';

const MonthlyTrends: React.FC<IMonthlyTrendsProps> = ({ chartData }) => {
  const { productInfo } = useModel('productInfo');
  const country = productInfo?.country;
  const currentMonth = getCountryTimezone(country!, 'M');

  const option = {
    grid: {
      left: 40,
      right: 10,
      bottom: 20,
      top: 25,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(0,0,0,0.65)', // 黑色背景
      borderColor: '#444',
      textStyle: {
        color: '#fff', // 白色文字
      },
      formatter: (params: { dataIndex: number }[]) => {
        const dataPoint = chartData[params[0].dataIndex];
        return `
          <strong>${dataPoint.month}</strong><br/>
          需求: ${dataPoint.demond}%<br/>
          趋势: ${dataPoint.trend}
        `;
      },
    },
    xAxis: {
      type: 'category',
      data: chartData.map(item => item.month),
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
      },
    },
    series: [
      {
        name: '市场需求',
        type: 'bar',
        data: chartData.map(item => item.demond),
        itemStyle: {
          color: (params: { dataIndex: number }) => {
            // @ts-ignore
            const month = parseInt(chartData[params.dataIndex].month, 10);
            return month === parseInt(currentMonth, 10) ? colorPrimary : '#1890ff';
          },
        },
      },
    ],
  };

  return <ReactECharts option={option} style={{ height: '100%', width: '100%' }} />;
};

export default MonthlyTrends;