// 基础映射定义
interface BaseMapping {
  path: string | string[]; // 原始数据路径，支持多个备选路径
  defaultValue?: any;
  transformer?: (value: any, sourceObject: any) => any; // 转换函数可以接收整个源对象以进行更复杂的计算
}

// 针对不同类型的映射定义
interface PrimitiveMapping extends BaseMapping {
  type: 'primitive';
}

interface ObjectMapping extends BaseMapping {
  type: 'object';
  // 如果是对象，我们需要一个子配置
  schema: { [key: string]: Mapping };
}

interface ArrayMapping extends BaseMapping {
  type: 'array';
  // 如果是数组，我们需要针对数组中每个元素的映射配置
  // 如果 schema 为空，表示数组元素是原始值（string、number等）
  schema?: { [key: string]: Mapping };
}

// 最终的映射可以是这三种中的任意一种
export type Mapping = PrimitiveMapping | ObjectMapping | ArrayMapping;
