import React, { useEffect } from 'react';
import { history, useModel, useIntl } from '@umijs/max';
import { Button, Result } from 'antd';


const NoFoundPage: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { permissions } = initialState || {};
  const canViewShopAuth = permissions?.shop_auth_perm === 'MANAGE';
  const hasShop = permissions?.has_shop;

  useEffect(() => {
    if (canViewShopAuth) {
      if (hasShop) {
        history.push('/admin/listing');
      } else {
        history.push('/authorization/shop-authorization');
      }
    } else {
      if (hasShop) {
        history.push('/admin/listing');
      } else {
        history.push('/authorization/unavailable');
      }
    }

  }, [canViewShopAuth, hasShop]);

  return (
    <Result
      status="403"
      title="403"
      subTitle={useIntl().formatMessage({ id: 'pages.403.subTitle' })}
      extra={
        <Button type="primary" onClick={() => history.push('/')}>
          {useIntl().formatMessage({ id: 'pages.403.buttonText' })}
        </Button>
      }
    />
  )
};

export default NoFoundPage;
