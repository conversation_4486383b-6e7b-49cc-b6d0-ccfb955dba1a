import React, { useEffect, useState } from 'react';
import { CloseCircleOutlined, SmileOutlined } from '@ant-design/icons';
import { Button, Card, Typography, Space } from 'antd';
import { useModel } from '@umijs/max';
import { AuthUtils } from '@/utils/auth';

const DemoTip: React.FC = () => {
  const [isGuestTipVisible, setIsGuestTipVisible] = useState(false);
  const { initialState } = useModel('@@initialState');
  useEffect(() => {
    const isGuestMode = localStorage.getItem('isGuestMode') === 'true';
    const isTipClosed = sessionStorage.getItem('guestTipClosed') === 'true';
    if (isGuestMode && !isTipClosed) {
      setIsGuestTipVisible(true);
    } else {
      setIsGuestTipVisible(false);
    }
  }, [initialState?.currentUser]);

  const handleCloseGuestTip = () => {
    sessionStorage.setItem('guestTipClosed', 'true');
    setIsGuestTipVisible(false);
  };

  const handleLogout = () => {
    AuthUtils.logout(false);
  };

  return (
    <>
      {isGuestTipVisible && (
        <Card
          style={{
            position: 'fixed',
            right: 20,
            bottom: 20,
            width: 380,
            backgroundColor: '#ED1000',
            color: 'white',
            borderRadius: 12,
            zIndex: 1000,
          }}
          bodyStyle={{ padding: '20px 24px' }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
            <Space align="center" size="middle">
              <SmileOutlined style={{ fontSize: 24, color: 'white' }} />
              <Typography.Title level={4} style={{ color: 'white', margin: 0 }}>
                功能演示
              </Typography.Title>
            </Space>
            <Button
              type="text"
              icon={<CloseCircleOutlined style={{ color: 'white', fontSize: 16 }} />}
              onClick={handleCloseGuestTip}
              style={{ marginRight: -12, marginTop: -12 }}
            />
          </div>
          <Typography.Paragraph style={{ color: 'white', marginBottom: 20 }}>
            当前为体验模式,所有数据都是模拟信息!
          </Typography.Paragraph>
          <Space size="middle">
            <Button
              // shape="round"
              style={{ backgroundColor: 'white', color: '#ED1000', borderColor: '#ED1000', fontWeight: 'bold' }}
              onClick={handleLogout}
            >
              立即授权
            </Button>
            <Button
              // shape="round"
              style={{ backgroundColor: 'white', color: '#ED1000', borderColor: '#ED1000', fontWeight: 'bold' }}
              onClick={handleLogout}
            >
              退出演示
            </Button>
          </Space>
        </Card>
      )}
    </>
  );
};

export default DemoTip; 