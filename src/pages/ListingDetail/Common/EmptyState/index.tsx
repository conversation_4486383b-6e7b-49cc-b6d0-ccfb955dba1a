import React from 'react';
import { Flex, Typography } from 'antd';
import noReport from '@/assets/images/no-report.svg';

const { Text } = Typography;

interface EmptyStateProps {
  /** 空状态图片的宽度 */
  imageWidth?: number;
  /** 空状态显示的文本 */
  text?: string;
  /** 自定义图片源 */
  imageSrc?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 图片的alt属性 */
  imageAlt?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  imageWidth = 180,
  text = '暂无数据',
  imageSrc = noReport,
  style,
  imageAlt = '空状态图片'
}) => {
  return (
    <Flex 
      align='center' 
      style={{ 
        flexDirection: 'column', 
        gap: 16, 
        marginTop: '20vh', 
        marginBottom: '20vh',
        ...style
      }}
    >
      <img width={imageWidth} src={imageSrc} alt={imageAlt} />
      <Text>{text}</Text>
    </Flex>
  );
};

export default EmptyState;