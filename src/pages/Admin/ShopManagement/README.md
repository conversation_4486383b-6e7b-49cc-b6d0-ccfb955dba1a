# 授权&设置页面

## 功能概述

授权&设置页面用于管理亚马逊店铺信息，包括店铺的基本信息和授权状态管理。

## 主要功能

### 1. 店铺列表展示
- 显示店铺的基本信息：市场、店铺名称、店铺简称、站点、卖家ID
- 显示授权状态：店铺授权状态、广告授权状态
- 显示时间信息：创建时间、更新时间

### 2. 筛选和搜索功能
- **市场筛选**: 按北美、欧洲、亚太等市场筛选
- **店铺筛选**: 按店铺名称筛选
- **Seller ID搜索**: 支持卖家ID搜索
- **站点筛选**: 按美国、英国、德国等站点筛选
- **授权状态筛选**: 按店铺授权状态和广告授权状态筛选
- **综合搜索**: 支持店铺名称/物流/卖家ID的模糊搜索

### 3. 店铺操作
- **添加店铺**: 通过模态框添加新店铺
- **编辑店铺**: 修改店铺基本信息
- **删除店铺**: 删除不需要的店铺
- **授权操作**: 一键进行店铺授权和广告授权

### 4. 分页功能
- 支持自定义每页显示数量
- 支持快速跳转到指定页码
- 显示总数据量统计

## 技术实现

### API 接口
- `GET /api/shops` - 获取店铺列表
- `GET /api/shops/filter-options` - 获取筛选选项
- `POST /api/shops` - 添加店铺
- `PUT /api/shops/:id` - 更新店铺
- `DELETE /api/shops/:id` - 删除店铺
- `POST /api/shops/:id/authorize` - 店铺授权

### 组件结构
```
ShopManagement/
├── index.tsx           # 主页面组件
├── index.less          # 样式文件
├── components/
│   ├── AddShop.tsx     # 添加店铺模态框
│   └── index.ts        # 组件导出
└── README.md           # 说明文档
```

### 状态管理
- 使用 React Hooks 管理组件状态
- 分离数据状态、UI状态和筛选条件状态
- 支持分页状态的独立管理

### 样式设计
- 参照 Ant Design 设计规范
- 响应式布局设计
- 授权状态的可视化展示

## 使用说明

1. **访问页面**: 通过侧边栏菜单"授权&设置"进入页面
2. **筛选数据**: 使用顶部筛选条件快速定位所需店铺
3. **添加店铺**: 点击"添加店铺"按钮填写店铺信息
4. **管理授权**: 点击"立即授权"进行店铺或广告授权
5. **编辑删除**: 通过操作列的更多按钮进行编辑和删除

## 注意事项

- 店铺删除操作不可恢复，请谨慎操作
- 授权状态变更后需要刷新页面查看最新状态
- 筛选条件会自动保存，直到页面刷新
