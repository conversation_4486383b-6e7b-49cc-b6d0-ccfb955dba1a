import React, { useEffect, useState } from 'react';
import { getOperationLog, getOperationLogFilterOptions, OperationLogItem } from '@/services/ibidder_api/user';
import { Table, DatePicker, Select, message, Row, Col, Space } from 'antd';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

// 定义快捷时间范围
const rangePresets = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().subtract(1, 'd').startOf('day'), dayjs().subtract(1, 'd').endOf('day')] },
  { label: '最近3天', value: [dayjs().subtract(3, 'd').startOf('day'), dayjs()] },
  { label: '最近7天', value: [dayjs().subtract(7, 'd').startOf('day'), dayjs()] },
  { label: '最近30天', value: [dayjs().subtract(30, 'd').startOf('day'), dayjs()] },
];

interface OperationLogProps {
  isActive: boolean;
}

const OperationLog: React.FC<OperationLogProps> = ({ isActive }) => {
  const [logData, setLogData] = useState<OperationLogItem[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10 });
  const [filters, setFilters] = useState<{ user_id?: number; start_time?: string; end_time?: string }>({});
  const [userOptions, setUserOptions] = useState<{ label: string; value: number }[]>([]);
  const [isOptionsLoaded, setIsOptionsLoaded] = useState(false);

  useEffect(() => {
    if (isActive) {
      getOperationLogFilterOptions().then((filterRes) => {
        if (filterRes.code === 200) {
          const options = filterRes.data.user_map.map(
            (user: { user_name: string; user_id: number }) => ({
              label: user.user_name,
              value: user.user_id,
            }),
          );
          setUserOptions(options);
          setIsOptionsLoaded(true); // Signal that options are ready
        } else {
          message.error(filterRes.message || '获取筛选选项失败');
        }
      });
    } else {
      setIsOptionsLoaded(false);
    }
  }, [isActive]);

  useEffect(() => {
    if (isActive && isOptionsLoaded) {
      setLoading(true);
      getOperationLog({
        page: pagination.current,
        page_size: pagination.pageSize,
        ...filters,
      }).then((logRes) => {
        if (logRes.code === 200) {
          setLogData(logRes.data.list);
          setTotal(logRes.data.total);
        } else {
          message.error(logRes.message || '获取日志失败');
        }
      }).finally(() => {
        setLoading(false);
      });
    }
  }, [isActive, isOptionsLoaded, pagination, filters]);

  const handleFilterChange = (changedFilters: any) => {
    const newFilters = { ...filters, ...changedFilters };
    setFilters(newFilters);
    setPagination(p => ({ ...p, current: 1 }));
  };
  
  const handleTableChange = (newPagination: any) => {
    setPagination(p => ({
      ...p,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    }));
  };
  
  const columns = [
    {
      title: '操作时间',
      dataIndex: 'operation_time',
      key: 'operation_time',
      width: 200,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作人',
      dataIndex: 'user_id',
      key: 'user_id',
      width: 150,
      render: (userId: number) =>
        userOptions.find((u) => u.value === userId)?.label || userId,
    },
    {
      title: '操作类型',
      dataIndex: 'operation_type_text',
      key: 'operation_type_text',
      width: 150,
    },
    {
      title: '操作详情',
      dataIndex: 'operation_detail',
      key: 'operation_detail',
    },
  ];

  return (
    <div>
      <Row justify="end" style={{ marginBottom: 16 }}>
        <Col>
          <Space>
            <Select
              placeholder="操作人"
              style={{ width: 150 }}
              allowClear
              options={userOptions}
              onChange={(value) => handleFilterChange({ user_id: value })}
            />
            <RangePicker
              presets={rangePresets as any}
              onChange={(dates, dateStrings) => {
                console.log(dates, dateStrings);
                handleFilterChange({ start_time: dateStrings[0], end_time: dateStrings[1] });
              }}
            />
          </Space>
        </Col>
      </Row>
      <Table
        loading={loading}
        columns={columns}
        dataSource={logData}
        rowKey="id"
        sticky
        pagination={{
          ...pagination,
          total,
          showTotal: (t) => `共 ${t} 条`,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
        onChange={handleTableChange}
      />
    </div>
  );
};

export default OperationLog;
