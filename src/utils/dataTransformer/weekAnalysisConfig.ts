import type { Mapping } from './dataTransformer.types';

// 为 WeekAnalysisData 创建映射配置
export const weekAnalysisConfig: { [K in keyof Strategy.WeekAnalysisData]: Mapping } = {
  start_date: { type: 'primitive', path: 'start_date', defaultValue: '' },
  end_date: { type: 'primitive', path: 'end_date', defaultValue: '' },
  forecast: {
    type: 'object',
    path: 'forecast',
    schema: {
      overview: { type: 'primitive', path: 'overview', defaultValue: '' },
      market_preview: { type: 'array', path: 'market_preview' },
      metrics_forecast: {
        type: 'object',
        path: 'metrics_forecast',
        schema: {
          traffic: { type: 'primitive', path: 'traffic', defaultValue: '' },
          spend: { type: 'primitive', path: 'spend', defaultValue: '' },
          sales: { type: 'primitive', path: 'sales', defaultValue: '' },
          acos: { type: 'primitive', path: 'acos', defaultValue: '' },
          cvr: { type: 'primitive', path: 'cvr', defaultValue: '' }
        }
      }
    }
  },
  revision_history: { type: 'primitive', path: 'revision_history', defaultValue: {} },
  weekly_strategy: {
    type: 'array',
    path: 'weekly_strategy',
    schema: {
      week_start_date: { type: 'primitive', path: 'week_start_date', defaultValue: '' },
      strategy: { type: 'primitive', path: 'strategy', defaultValue: '' }
    }
  },
  key_dates: {
    type: 'array',
    path: 'key_dates',
    schema: {
      start_date: { type: 'primitive', path: 'start_date', defaultValue: '' },
      end_date: { type: 'primitive', path: 'end_date', defaultValue: '' },
      type: { type: 'array', path: 'type' },
      name: { type: 'primitive', path: 'name', defaultValue: '' },
      confidence: { type: 'primitive', path: 'confidence', defaultValue: '' },
      significance: { type: 'primitive', path: 'significance', defaultValue: '' },
      expected_impact: {
        type: 'object',
        path: 'expected_impact',
        schema: {
          traffic: { type: 'primitive', path: 'traffic', defaultValue: '' },
          conversion: { type: 'primitive', path: 'conversion', defaultValue: '' },
          competition: { type: 'primitive', path: 'competition', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      strategy: { type: 'primitive', path: 'strategy', defaultValue: '' }
    }
  },
  swot: {
    type: 'object',
    path: 'swot',
    schema: {
      strengths: { type: 'array', path: 'strengths' },
      weaknesses: { type: 'array', path: 'weaknesses' },
      opportunities: { type: 'array', path: 'opportunities' },
      threats: { type: 'array', path: 'threats' }
    },
  },
  ads_suggestion: {
    type: 'object',
    path: 'ads_suggestion',
    schema: {
      approach: { type: 'primitive', path: 'approach', defaultValue: '' },
      primary_goal: {
        type: 'object',
        path: 'primary_goal',
        schema: {
          goal: { type: 'primitive', path: 'goal', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      other_goals: { type: 'array', path: 'other_goals' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
      weekly_strategy: {
        type: 'array',
        path: 'weekly_strategy',
        schema: {
          week_start_date: { type: 'primitive', path: 'week_start_date', defaultValue: '' },
          strategy: { type: 'primitive', path: 'strategy', defaultValue: '' }
        }
      }
    }
  },
  non_ads_suggestion: { type: 'array', path: 'non_ads_suggestion' },
  google_searchs: {
    type: 'object',
    path: 'google_searchs',
    schema: {
      rendered_content: { type: 'primitive', path: 'rendered_content', defaultValue: '' },
      web_search_queries: { type: 'primitive', path: 'web_search_queries', defaultValue: '' },
      grounding_chunks: {
        type: 'array',
        path: 'grounding_chunks',
        schema: {
          title: { type: 'primitive', path: 'title', defaultValue: '' },
          url: { type: 'primitive', path: 'url', defaultValue: '' }
        }
      }
    }
  }
};
