import React, { useState, useEffect, useLayoutEffect, useRef, useCallback } from 'react';
import { Button, Space, message, Select, Tooltip, Checkbox } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';
import { useSearchParams } from '@umijs/max';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);
import styles from './index.less';
import { getReportDatesWithVer, setDefaultReport } from '@/services/ibidder_api/operation';
import { getDayOfWeek } from '@/utils/bus';

interface DateNavigatorProps {
  initialDate?: string;
  current_time: string;
  job_id?: string;
  dateType: 'single' | 'range'; // 新增：区分单日期和区间日期
  es_id: string;
  // 新增：API调用回调函数
  onDateChange?: (apiDate: string, isFirst?: boolean) => Promise<void>;
  onVersionChange?: (esId: string) => Promise<void>;
}

// API 返回的日期数据类型
interface DateItem {
  start_time: string;
  end_time: string;
  target_week: string;
  ver_list: {
    ver: number;
    is_select: number;
  }[];
}

// API 响应类型
interface GetAllReportDatesResponse {
  code: number;
  message?: string;
  data: {
    date: DateItem[];
    asin: string;
    profile_id: string;
    job_id: string;
  };
}

// 版本相关状态
interface VersionState {
  current: number;
  default: number;
}

/* 日期导航器组件 根据传入的 country 获取时区对应的日期 */
const DateNavigator: React.FC<DateNavigatorProps> = (props) => {
  const {
    initialDate,
    job_id,
    dateType = 'single',
    es_id,
    onDateChange,
    onVersionChange
  } = props
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;
  const scrollWrapperRef = useRef<HTMLDivElement>(null);

  // 状态管理优化：合并相关状态
  const [selectedDate, setSelectedDate] = useState('');
  const [dateItems, setDateItems] = useState<DateItem[]>([]);
  const [showNavButtons, setShowNavButtons] = useState(false);
  const [selectedDateItem, setSelectedDateItem] = useState<DateItem | null>(null);
  const [versionState, setVersionState] = useState<VersionState>({
    current: 0,
    default: 0,
  });

  // 更新版本状态的工具函数
  const updateVersionState = useCallback((dateItem: DateItem) => {
    const currentVersion = dateItem.ver_list[0]?.ver || 0;
    const defaultVersion = dateItem.ver_list.find(v => v.is_select === 1)?.ver || 0;
    setVersionState({
      current: currentVersion,
      default: defaultVersion,
    });
  }, []);

  // 获取当前显示的日期数据
  const loadDateData = useCallback(async (): Promise<DateItem[]> => {
    try {
      const res = await getReportDatesWithVer({
        asin,
        job_id: job_id + '',
        profile_id,
      }) as GetAllReportDatesResponse;

      if (res.code === 200 && res.data?.date) {
        const items = res.data.date;
        setDateItems(items);
        return items;
      }
      return [];
    } catch (error) {
      return [];
    }
  }, [asin, job_id, profile_id]);

  // 查找并设置选中的日期项
  const findAndSetSelectedDateItem = useCallback((selectedDate: string) => {
    const selectedIndex = dateItems.findIndex(item => item.target_week === selectedDate);
    if (selectedIndex === -1 || !dateItems[selectedIndex]) {
      message.error('无法找到对应的日期数据');
      return null;
    }

    const selectedDateItem = dateItems[selectedIndex];
    setSelectedDateItem(selectedDateItem);

    if (selectedDateItem.ver_list?.length > 0) {
      updateVersionState(selectedDateItem);
    }

    return selectedDateItem;
  }, [dateItems, updateVersionState]);

  useEffect(() => {
    // 加载日期数据
    const initializeData = async () => {
      const items = await loadDateData();
      if (items.length > 0) {
        // 如果存在 initialDate ，则初次加载，应当把对应的设置成 initialDate 相对应的日期
        const targetItem = (initialDate && items.find(item => item.start_time === initialDate)) || items[0];
        setSelectedDate(targetItem.start_time);
        setSelectedDateItem(targetItem);
        updateVersionState(targetItem);
      }
    };
    initializeData();
  }, [asin, profile_id, job_id, dateType, loadDateData, updateVersionState, initialDate]);

  useLayoutEffect(() => {
    const checkOverflow = () => {
      if (scrollWrapperRef.current) {
        const { scrollWidth, clientWidth } = scrollWrapperRef.current;
        setShowNavButtons(scrollWidth > clientWidth);
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, [dateItems]);

  // 滚动处理函数优化
  const createScrollHandler = useCallback((direction: 'prev' | 'next') => () => {
    if (scrollWrapperRef.current) {
      const scrollAmount = scrollWrapperRef.current.offsetWidth * 0.5;
      const scrollDirection = direction === 'prev' ? -scrollAmount : scrollAmount;
      scrollWrapperRef.current.scrollBy({
        left: scrollDirection,
        behavior: 'smooth'
      });
    }
  }, []);

  const handlePrev = createScrollHandler('prev');
  const handleNext = createScrollHandler('next');

  const handleDateClick = useCallback(async (selectedDate: string) => {
    setSelectedDate(selectedDate);

    if (!asin || !profile_id) {
      console.error('缺少必要参数 asin 或 profile_id');
      return;
    }

    // 根据显示的日期字符串找到对应的原始数据项
    const selectedDateItem = findAndSetSelectedDateItem(selectedDate);
    if (!selectedDateItem) return;

    // 判断是否是第一个日期（最新的日期）
    const isFirstDate = dateItems.length > 0 && dateItems[0].start_time === selectedDateItem.start_time;

    // 使用 start_time 作为 API 调用的日期参数
    if (onDateChange) {
      await onDateChange(selectedDateItem.start_time, isFirstDate);
    }
  }, [asin, profile_id, findAndSetSelectedDateItem, onDateChange, dateItems]);

  // 版本切换处理函数
  const handleVersionChange = useCallback(async (version: number) => {
    setVersionState(prev => ({ ...prev, current: version }));
    if (!selectedDateItem) return;

    const esIdPrefix = es_id.split('_')[0];
    if (esIdPrefix && onVersionChange) {
      const targetEsId = `${esIdPrefix}_${version}`;
      await onVersionChange(targetEsId);
    }
  }, [selectedDateItem, es_id, onVersionChange]);

  // 设置默认版本
  const handleSetDefaultVersion = useCallback(async (version: number) => {
    try {
      await setDefaultReport({
        asin,
        current_time: props.current_time,
        job_id: job_id + '',
        profile_id,
        ver: version,
      });

      // 重新加载数据，保持当前选中的日期
      const items = await loadDateData();
      if (selectedDateItem && items.length > 0) {
        // 找到当前选中日期对应的更新后的数据
        const currentSelectedItem = items.find(item => item.start_time === selectedDateItem.start_time);
        if (currentSelectedItem) {
          setSelectedDateItem(currentSelectedItem);
          updateVersionState(currentSelectedItem);
        }
      }

      // message.success('设置默认版本成功');
    } catch (error) {
      console.error('设置默认版本失败, 请联系管理员。');
    }
  }, [asin, props.current_time, job_id, profile_id, selectedDateItem, loadDateData, updateVersionState]);

  // 格式化日期显示
  const formatDateDisplay = useCallback((item: DateItem) => {
    if (job_id === 'market_report_month') {
      return dayjs(item.start_time).format('YYYY-MM月');
    }
    return dateType === 'single' ?
      <div style={{ lineHeight: '1.3em', padding: '2px 0' }}>
        {item.start_time}
        <br />
        {getDayOfWeek(item.start_time)}
      </div>
      :
      `${item.start_time} ~ ${item.end_time}`;
  }, [job_id, dateType]);

  // 判断按钮是否应该高亮
  const isDateButtonActive = useCallback((item: DateItem) => {
    return job_id === 'market_report_month'
      ? dayjs(selectedDate).isSame(dayjs(item.start_time), 'month')
      : item.start_time === selectedDate;
  }, [job_id, selectedDate]);

  const currentVerIsDefault = versionState.current === versionState.default;

  return (
    <div className={styles.container}>
      <div className={styles.dateNavigatorWrapper}>
        <div className={styles.dateNavigatorContainer}>
          {showNavButtons && (
            <Button icon={<LeftOutlined />} type="text" onClick={handlePrev} size="small" />
          )}
          <div className={styles.dateButtonsWrapper} ref={scrollWrapperRef}>
            <Space size="middle">
              {dateItems.map((item) => (
                <Button
                  key={item.start_time}
                  type={isDateButtonActive(item) ? 'primary' : 'default'}
                  onClick={() => handleDateClick(item.start_time)}
                  size='small'
                  className={styles.dateButton}
                >
                  {formatDateDisplay(item)}
                </Button>
              ))}
            </Space>
          </div>
          {showNavButtons && (
            <Button icon={<RightOutlined />} type="text" onClick={handleNext} size="small" />
          )}
        </div>
      </div>
      {/* 修订历史部分 */}
      {selectedDateItem?.ver_list && selectedDateItem.ver_list.length > 1 && (
        <Space size={16}>
          <span className={styles.revisionHistory}>
            <span className={styles.revisionHistoryLabel}>修订历史： </span>
            <Select
              value={versionState.current}
              onChange={handleVersionChange}
              options={selectedDateItem.ver_list.map(item => ({
                label: `V${item.ver}`,
                value: item.ver,
              }))}
              style={{ width: 65 }}
              size='small'
            />
          </span>
          <Tooltip title={currentVerIsDefault ? null : '将当前版本设置为默认投放策略'}>
            <Checkbox
              disabled={currentVerIsDefault}
              onClick={() => handleSetDefaultVersion(versionState.current)}
              checked={currentVerIsDefault}
            >
              {currentVerIsDefault ? '已默认' : '设为默认'}
            </Checkbox>
          </Tooltip>
        </Space>
      )}
    </div>
  );
};

export default DateNavigator;
