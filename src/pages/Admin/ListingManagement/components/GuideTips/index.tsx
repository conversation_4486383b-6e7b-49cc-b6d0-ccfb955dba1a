import React, { useState } from 'react';
import starIcon from '@/assets/images/icon/star.svg';
import styles from './index.less';
import { Button, Card } from 'antd';

// 本页顶部“指南”折叠组件（默认展开）
const GuideBox: React.FC = () => {
  const [expanded, setExpanded] = useState(false);
  const toggle = () => setExpanded(prev => !prev);

  return (
    <Card className={styles.guideBox} bodyStyle={{ padding: '12px 12px 12px 12px' }}>
      <img src={starIcon} alt="star" className={styles.guideIcon} />
      <Button className={styles.guideToggle} onClick={toggle} type="link">
        {expanded ? '关闭指南' : '查看指南'}
      </Button>
      <div className={`${styles.guideContent} ${expanded ? styles.expanded : ''}`}>
        <div>为了更大化您的广告投资回报率（ROI），请在添加产品前，参考以下指南。</div>
        <div style={{ color: '#ED1000' }}>‼️ 温馨提示：如果您刚完成广告授权，系统需要一定时间拉取数据，请稍等片刻（通常需要约 1~2 小时）后再添加产品。</div>

        <div>✅ 推荐添加</div>
        <ul className={styles.ul}>
          <li>主力产品：预算充足（≳$50/天）、数据丰富的产品。</li>
          <li>潜力产品：有明确提升空间，销售环比增长中，AI能进一步优化。</li>
        </ul>

        <div>❌ 暂不推荐</div>
        <ul className={styles.ul}>
          <li>新品/零销量：尚未积累转化数据时建议先做基础投放或站内运营。</li>
          <li>无库存/即将断货产品：AI会按数据自动分配预算，断货产品可能影响效果。</li>
        </ul>
      </div>
    </Card>
  );
};

export default GuideBox;