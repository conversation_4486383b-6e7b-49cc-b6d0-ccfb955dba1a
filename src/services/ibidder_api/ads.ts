// @ts-ignore
/* eslint-disable */
import { request } from '@/request';

/** Get Search Terms GET /api/v1/ads/sp/search-terms */
export async function getSearchTerms(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.GetSearchTermsParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/v1/ads/sp/search-terms', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取Listing的广告活动列表 POST /api/v1/ads/campaign/list */
export async function getCampaignList(params: {
  parent_asin: string;
  profile_id: number;
  campaign_name?: string;
  page: number;
  page_size: number;
}) {
  return request<any>('/api/v1/ads/campaign/list', {
    method: 'POST',
    data: params,
  });
}

/** Listing的Campaign批量开关 POST /api/v1/ads/campaign/batch-toggle */
export async function batchToggleCampaign(params: {
  parent_asin: string;
  profile_id: number;
  campaign_list: {
    campaign_id: string;
    campaign_type: string;
  }[];
  enabled: boolean;
}) {
  return request<any>('/api/v1/ads/campaign/batch-toggle', {
    method: 'POST',
    data: params,
  });
}
