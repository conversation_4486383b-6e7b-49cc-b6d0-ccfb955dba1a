import type { Mapping } from './dataTransformer.types';
import { processNumberOrString } from '../bus';

// 专门处理 Monthly_trends 的归一化函数
export function monthlyTrendsTransformer(data: any, sourceData: any): Strategy.MonthlyTrendItem[] {
  // 适配可能的字段名
  const _data = data || sourceData.market_trends?.monthly_trends
  if (!_data || typeof _data !== 'object') {
    return [];
  }

  // 处理数组格式：[{"1": {"trend": "...", "demond": "85%"}}, {"2": {...}}, ...]
  if (Array.isArray(_data)) {
    if (_data[0] && typeof _data[0] === 'object' && _data[0]['1']) {
      return _data.map((item) => {
        if (item && typeof item === 'object') {
          const monthKey = Object.keys(item)[0]; // 获取月份键（"1", "2", 等）
          const monthData = item[monthKey];

          if (monthData && typeof monthData === 'object') {
            return {
              month: `${monthKey}月`,
              demond: processNumberOrString(monthData.demond || monthData.demand || monthData.demand_index || '0'),
              trend: monthData.trend || '',
            };
          }
        }
        return {
          month: '',
          demond: 0,
          trend: '',
        };
      }).filter(item => item.month); // 过滤掉无效的条目
    } else {
      return _data.map((item) => {
        return {
          ...item,
          demond: processNumberOrString(item.demond || item.demand || item.demand_index || '0'),
          month: `${item.month}月`,
        }
      })
    }
  }

  // 处理对象格式：{"1": {"trend": "...", "demond": "85%"}, "2": {...}, ...}
  const result: Strategy.MonthlyTrendItem[] = [];
  for (const [month, monthData] of Object.entries(_data)) {
    if (monthData && typeof monthData === 'object') {
      const item = monthData as any;
      result.push({
        month: `${month}月`,
        demond: processNumberOrString(item.demond || item.demand || item.demand_index || '0'),
        trend: item.trend || '',
      });
    }
  }
  return result.length > 0 ? result : [];
}

export const marketTrendsConfig: { [K in keyof Strategy.MarketTrends]: Mapping } = {
  monthly_trends: {
    type: 'array',
    path: 'monthly_trends',
    schema: {
      month: { type: 'primitive', path: 'month', defaultValue: '' },
      demond: { type: 'primitive', path: 'demond', defaultValue: '' },
      trend: { type: 'primitive', path: 'trend', defaultValue: '' },
    },
    transformer: monthlyTrendsTransformer
  }
}
