import type { Mapping } from './dataTransformer.types';

// 为 WeekStrategyData 创建映射配置
export const weekStrategyConfig: { [K in keyof Strategy.WeekStrategyData]: Mapping } = {
  start_date: { type: 'primitive', path: 'start_date', defaultValue: '' },
  end_date: { type: 'primitive', path: 'end_date', defaultValue: '' },
  current_time: { type: 'primitive', path: 'current_time', defaultValue: '' },
  approach: { type: 'primitive', path: 'approach', defaultValue: '' },
  rationale: { type: 'primitive', path: 'rationale', defaultValue: '' },
  revision_history: { type: 'primitive', path: 'revision_history', defaultValue: {} },
  ads_suggestion: {
    type: 'object',
    path: 'ads_suggestion',
    schema: {
      approach: { type: 'primitive', path: 'approach', defaultValue: '' },
      primary_goal: {
        type: 'object',
        path: 'primary_goal',
        schema: {
          goal: { type: 'primitive', path: 'goal', defaultValue: '' },
          rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
        }
      },
      other_goals: { type: 'array', path: 'other_goals' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
    }
  },
  primary_goal: {
    type: 'object',
    path: 'primary_goal',
    schema: {
      goal: { type: 'primitive', path: 'goal', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
    }
  },
  other_goals: { type: 'array', path: 'other_goals' },
  week_budget: {
    type: 'object',
    path: 'week_budget',
    schema: {
      typical: { type: 'primitive', path: 'typical', defaultValue: '' },
      min: { type: 'primitive', path: 'min', defaultValue: '' },
      max: { type: 'primitive', path: 'max', defaultValue: '' },
      last_week_budget: { type: 'primitive', path: 'last_week_budget', defaultValue: '' },
      change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' },
      rationale: { type: 'primitive', path: 'rationale', defaultValue: '' }
    }
  },
  bid_adjustment_range: {
    type: 'object',
    path: 'bid_adjustment_range',
    schema: {
      min: { type: 'primitive', path: 'min', defaultValue: '' },
      max: { type: 'primitive', path: 'max', defaultValue: '' },
      rational: { type: 'primitive', path: 'rational', defaultValue: '' }
    }
  },
  // 动态键对象：{ [date: string]: { approach, guideline, budget_range, bid_adjustment_range } }
  daily_strategy_suggestion: { type: 'primitive', path: 'daily_strategy_suggestion', defaultValue: '' },
  week_expected_result: {
    type: 'object',
    path: 'week_expected_result',
    schema: {
      spend: {
        type: 'object',
        path: 'spend',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      },
      sales: {
        type: 'object',
        path: 'sales',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      },
      orders: {
        type: 'object',
        path: 'orders',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      },
      cvr: {
        type: 'object',
        path: 'cvr',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      },
      acos: {
        type: 'object',
        path: 'acos',
        schema: {
          typical: { type: 'primitive', path: 'typical', defaultValue: '' },
          min: { type: 'primitive', path: 'min', defaultValue: '' },
          max: { type: 'primitive', path: 'max', defaultValue: '' },
          last_week: { type: 'primitive', path: 'last_week', defaultValue: '' },
          change_from_last_week: { type: 'primitive', path: 'change_from_last_week', defaultValue: '' }
        }
      }
    }
  },
  tips_for_week_after_target_week: { type: 'primitive', path: 'tips_for_week_after_target_week', defaultValue: '' }
};
