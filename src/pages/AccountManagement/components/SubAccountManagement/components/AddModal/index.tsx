import React, { useEffect, useState, useMemo } from 'react';
import {
  Checkbox,
  Col,
  Form,
  Input,
  Modal,
  Row,
  Select,
  Spin,
  Tag,
  message,
} from 'antd';
import styles from './style.less';
import {
  getAccountFilterOptions,
  getShopList,
  SubAccountItem,
  createSubAccount,
  editSubAccount,
} from '@/services/ibidder_api/user';
import { useModel } from '@umijs/max';

interface AddOrEditSubAccountModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  userInfo?: SubAccountItem;
  type: 'add' | 'edit';
}

const AddOrEditSubAccountModal: React.FC<AddOrEditSubAccountModalProps> = ({
  open,
  onCancel,
  onOk,
  userInfo,
  type,
}) => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [loading, setLoading] = useState(false);
  const [shopList, setShopList] = useState<any[]>([]);
  const [roleOptions, setRoleOptions] = useState<any[]>([]);
  const [adminId, setAdminId] = useState<number | undefined>();
  const [isShopDisabled, setIsShopDisabled] = useState(false);
  const [defaultSelected, setDefaultSelected] = useState<number[]>([]);

  const checkedShops = Form.useWatch('profile_id_list', form) || [];

  const shopMap = useMemo(() => {
    return shopList.reduce((acc, shop) => {
      acc[shop.profile_id] = shop.name;
      return acc;
    }, {} as Record<number, string>);
  }, [shopList]);

  const handleCloseTag = (removedId: number) => {
    const newCheckedShops = checkedShops.filter((id: number) => id !== removedId);
    form.setFieldsValue({ profile_id_list: newCheckedShops });
  };

  useEffect(() => {
    if (open) {
      if (type === 'edit') {
        form.setFieldsValue({
          user_name: userInfo?.user_name,
          role_id: userInfo?.role_id,
          phone: userInfo?.phone,
          email: userInfo?.email,
          profile_id_list: [],
        });
      } else {
        form.resetFields();
        setIsShopDisabled(false);
      }
      setLoading(true);
      Promise.all([
        getAccountFilterOptions(),
        getShopList(type === 'edit' ? { user_id: userInfo?.id } : {}),
      ])
        .then(([roleRes, shopRes]) => {
          if (roleRes.code === 200) {
            const currentAdminId = roleRes.data.role_map.find(
              (item: any) => item.role_type === 1,
            )?.role_id;
            setRoleOptions(roleRes.data.role_map);
            setAdminId(currentAdminId);
            if (type === 'edit' && userInfo?.role_id === currentAdminId) {
              setIsShopDisabled(true);
            } else {
              setIsShopDisabled(false);
            }
          }
          if (shopRes.code === 200) {
            const shopList = shopRes.data.profile_id_list.map((item: any) => ({
              ...item,
              name: `${item.country_code} - ${item.profile_name}`,
            }));
            setShopList(shopList);
            setDefaultSelected(shopRes.data.selected);
            form.setFieldsValue({
              profile_id_list: shopRes.data.selected,
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [open]);

  const handleOk = () => {
    if (loading) return;
    form.validateFields().then((values) => {
      setLoading(true);
      if (type === 'add') {
        createSubAccount(values).then((res) => {
          if (res.code === 200) {
            message.success('添加成功');
            onOk();
          } else {
            message.error(res.message);
          }
        }).finally(() => {
          setLoading(false);
        }).catch(() => {
          setLoading(false);
        });
      } else {
        editSubAccount({
          ...values,
          user_id: userInfo?.id,
        }).then((res) => {
          if (res.code === 200) {
            message.success('编辑成功');
            if (currentUser?.userid == userInfo?.id) {
              setInitialState((s) => ({ ...s, currentUser: {
                ...currentUser,
                name: values.user_name,
                phone: values.phone,
                email: values.email,
              } }));
            }
            onOk();
          } else {
            message.error(res.message);
          }
        }).finally(() => {
          setLoading(false);
        }).catch(() => {
          setLoading(false);
        });
      }
    });
  };
  const passwordValidator = (_: any, value: string) => {
    if (!value) {
      return Promise.resolve();
    }
    if (value.length < 8 || value.length > 20) {
      return Promise.reject(new Error('密码长度必须在8-20位之间'));
    }
    let types = 0;
    if (/[a-zA-Z]/.test(value)) types++;
    if (/\d/.test(value)) types++;
    if (/[^a-zA-Z0-9]/.test(value)) types++;
    if (types < 2) {
      return Promise.reject(new Error('密码必须包含字母、符号或数字中至少两项'));
    }
    return Promise.resolve();
  };

  const handleRoleChange = (roleId: number) => {
    if (roleId === adminId) {
      const allShopIds = shopList.map((shop) => shop.profile_id);
      form.setFieldsValue({ profile_id_list: allShopIds });
      setIsShopDisabled(true);
    } else {
      form.setFieldsValue({ profile_id_list: defaultSelected });
      setIsShopDisabled(false);
    }
  };

  return (
    <Modal
      open={open}
      onCancel={onCancel}
      onOk={handleOk}
      title={null}
      width={1280}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <Form form={form} layout="vertical">
          <div className={styles.title}>{type === 'add' ? '添加子账号' : '编辑信息'}</div>
          <Row gutter={24} className={styles.formContainer}>
            <Col span={10} className={styles.formCol}>
              <div className={styles.formTitle}>子账号信息</div>
              <div className={styles.formContent}>
                <Form.Item
                  label="用户名"
                  name="user_name"
                  rules={[{ required: true, message: '请输入子账号的昵称' }]}
                >
                  <Input placeholder="请输入子账号的昵称" autoComplete="off" />
                </Form.Item>
                <Form.Item
                  label="角色分配"
                  name="role_id"
                  rules={[{ required: true, message: '请选择角色' }]}
                >
                  <Select placeholder="请选择角色" onChange={handleRoleChange}>
                    {roleOptions.map((role) => (
                      <Select.Option key={role.role_id} value={role.role_id}>
                        {role.role_name}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item label="登录手机号" name="phone">
                  <Input placeholder="请输入登录手机号" autoComplete="off" />
                </Form.Item>
                <Form.Item
                  label="登录邮箱"
                  name="email"
                  rules={[
                    { required: true, message: '请输入登录邮箱' },
                    {
                      type: 'email',
                      message: '请输入正确的邮箱地址',
                    },
                  ]}
                >
                  <Input placeholder="请输入登录邮箱" autoComplete="off" />
                </Form.Item>
                {type === 'add' && (
                  <>
                    <Form.Item
                      label="登录密码"
                      name="password"
                      rules={[
                        { required: true, message: '请输入登录密码' },
                        { validator: passwordValidator },
                      ]}
                    >
                      <Input.Password placeholder="请输入登录密码" autoComplete="new-password" />
                    </Form.Item>
                    <Form.Item
                      label="确认密码"
                      name="confirm_password"
                      dependencies={['password']}
                      rules={[
                        { required: true, message: '请确认密码' },
                        ({ getFieldValue }) => ({
                          validator(_, value) {
                            if (!value || getFieldValue('password') === value) {
                              return Promise.resolve();
                            }
                            return Promise.reject(
                              new Error('两次输入的密码不一致'),
                            );
                          },
                        }),
                      ]}
                    >
                      <Input.Password placeholder="请确认密码" autoComplete="new-password" />
                    </Form.Item>
                  </>
                )}
                {/* <Form.Item label="添加到用户分组（可选）" name="group_id">
                  <Select placeholder="选择分组" />
                </Form.Item> */}
              </div>
            </Col>
            <Col span={14} className={styles.formCol}>
              <div className={styles.formTitle}>
                <span>数据权限</span>
                <span className={styles.formTitleTip}>（请选择该成员可访问的店铺）</span>
              </div>
              <div className={styles.formContent}>
                <Input.Search
                  placeholder="搜索店铺名称、Seller ID"
                  onChange={(e) => setSearchText(e.target.value)}
                  style={{ marginBottom: 8 }}
                  allowClear
                />
                <div
                  style={{
                    marginBottom: 8,
                    padding: '4px 0',
                    maxHeight: 105,
                    overflowY: 'auto',
                    // border: '1px solid #d9d9d9',
                    // borderRadius: '2px',
                  }}
                >
                  {checkedShops.length > 0 && (
                    checkedShops.map((id: number) =>
                      shopMap[id] ? (
                        <Tag
                          key={id}
                          closable={!isShopDisabled}
                          color={isShopDisabled ? 'default' : 'blue'}
                          onClose={(e) => {
                            e.preventDefault();
                            handleCloseTag(id);
                          }}
                          style={{ marginBottom: 4 }}
                        >
                          {shopMap[id]}
                        </Tag>
                      ) : null,
                    )
                  ) }
                </div>
                <Form.Item name="profile_id_list">
                  <Checkbox.Group style={{ width: '100%' }} disabled={isShopDisabled}>
                    <div style={{ width: '100%' }}>
                      {shopList.map((shop) => (
                        <div
                          key={shop.profile_id}
                          className={styles.checkboxItem}
                          style={{
                            display:
                              !searchText ||
                              shop.name.toLowerCase().includes(searchText.toLowerCase()) ||
                              shop.seller_id?.toLowerCase().includes(searchText.toLowerCase())
                                ? 'block'
                                : 'none',
                          }}
                        >
                          <Checkbox value={shop.profile_id}>{shop.name}</Checkbox>
                        </div>
                      ))}
                    </div>
                  </Checkbox.Group>
                </Form.Item>
              </div>
            </Col>
          </Row>
        </Form>
      </Spin>
    </Modal>
  );
};

export default AddOrEditSubAccountModal;
