import React, { useEffect, useState } from 'react';
import { Tabs, Pagination, Typography, DatePicker, Card } from 'antd';
import styles from './style.less';
import dayjs from 'dayjs';
import { getAIWorkHistory, getRoleAgentDetail } from '@/services/ibidder_api/operation';
import { useSearchParams, useModel } from '@umijs/max';
import { getAvatarByType } from '../Common/bus';
import { getDayOfWeek } from '@/utils/bus';
import type {
  DayStrategyModalData,
  WeekStrategyModalData,
  WeekMonthReportModalData,
  DayStrategyCatModalData
} from '@/models/globalModals';

const { Title } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const job_idMap: Record<string, string> = {
  'ads_strategy_day': '日广告投放策略',
  'ads_strategy_week': '周广告投放策略',
  'market_report_month': '月市场分析报告',
}

// 定义 setTitle 的参数接口
interface SetTitleParams {
  type: string;
  current_time?: string;
  start_date?: string;
  end_date?: string;
  date?: string;
  noDate?: boolean;
}

const roleMap = {
  all: '全部',
  strategyAgent: '广告策略师',
  operAgent: '广告优化师',
  marketAgent: '市场分析师',
};

const roleDescMap = {
  strategyAgent: '广告增长引擎',
  operAgent: 'ROI操盘手',
  marketAgent: '市场雷达',
};

// AI工作动态数据接口
interface AIWorkItem {
  role: 'strategyAgent' | 'operAgent' | 'marketAgent';
  job_id: string;
  current_country: string;
  current_time: string;
  start_time_local: string;
  currtent_timezone: string;
  bj_time: string;
  es_id: string;
  job_name: string;
  can_edit: boolean;
  profile_id: string;
  asin: string;
  date?: string;
  success: boolean;
  /** 报表周期的开始日期 */
  target_week: string;
  start_time?: string;
  end_time?: string;
  result: {
    ads_strategy_daypart: any;
    ads_strategy_week: any;
    ads_strategy_day: any;
    market_report_week: any;
    market_report_month: any;
    daypart_strategy: any;
    set_campaign_budget?: any;
    set_placement_bidding?: any;
  }
}

const AiWork: React.FC = () => {
  const [searchParams] = useSearchParams();
  const asin = searchParams.get('asin') as string;
  const profile_id = searchParams.get('profile_id') as string;

  // 全局弹框状态管理
  const {
    openDayStrategyModal,
    openWeekStrategyModal,
    openWeekReportModal,
    openMonthReportModal,
    openDayStrategyCatModal
  } = useModel('globalModals');
  const { productInfo } = useModel('productInfo');
  const currency = productInfo?.currency || '$';
  const [aiWorkData, setAiWorkData] = useState<AIWorkItem[]>([]);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [role, setRole] = useState('all');
  const [total, setTotal] = useState(0);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([null, null]);

  useEffect(() => {
    getAIWorkHistory<AIWorkItem>({
      asin,
      profile_id,
      page_no: pageNo,
      page_size: pageSize,
      role,
      start_time: dateRange?.[0]?.format('YYYY-MM-DD') || undefined,
      end_time: dateRange?.[1]?.format('YYYY-MM-DD') || undefined,
    }).then(res => {
      setAiWorkData(res.data.list);
      setTotal(res.data.total);
    }).catch(err => {
      console.error(err);
    });
  }, [pageNo, pageSize, role, asin, dateRange]);

  const getTitle = (params: SetTitleParams) => {
    const { type, current_time, start_date, end_date, date, noDate } = params;
    // 获取标题
    switch (type) {
      case 'ads_strategy_day':
        return `日广告投放策略（${dayjs(date).format('YYYY-MM-DD')} ${getDayOfWeek(date)}）`;
      case 'ads_strategy_week':
        if (noDate) {
          return `周广告投放策略`;
        } else {
          const formattedStartDate = dayjs(start_date).format('YYYY-MM-DD');
          const formattedEndDate = dayjs(start_date).add(6, 'day').format('YYYY-MM-DD');
          return `周广告投放策略（${formattedStartDate} ~ ${formattedEndDate}）`;
        }
      case 'market_report_week':
        return `周市场分析报告（${start_date} ~ ${end_date}）`;
      case 'market_report_month':
        return `月市场分析报告（${start_date} ~ ${end_date}）`;
      case 'day_strategy_act':
        return `广告优化调整（${current_time} ${getDayOfWeek(current_time)}）`;
      default:
        return '策略详情';
    }
  }




  const getShowDate = (item: AIWorkItem) => {
    if (item.job_id === 'ads_strategy_week') {
      const formattedStartDate = item.result.ads_strategy_week.start_date
      const formattedEndDate = item.result.ads_strategy_week.end_date
      return `${formattedStartDate} ~ ${formattedEndDate}`;
    }
    if (item.job_id === 'market_report_month') {
      const formattedStartDate = item.result.market_report_month.start_date
      const formattedEndDate = item.result.market_report_month.end_date
      return `${formattedStartDate} ~ ${formattedEndDate}`;
    }
    if (item.job_id === 'ads_strategy_day') {
      return item.result.ads_strategy_day?.date;
    }
  }

  /** 点击修改意见 */
  const clickPrev = async (item: AIWorkItem) => {
    let prevEsId = item.es_id;
    const parts = item.es_id.split('_');
    if (parts.length > 1) {
      const num = parseInt(parts[1]);
      if (num > 1) {
        prevEsId = `${parts[0]}_${num - 1}`;
      }
    }
    const res = await getRoleAgentDetail<any>({ es_id: prevEsId });
    if (res.code === 200) {
      const data = res.data

      // 根据job_id调用对应的弹框方法
      if (data.job_id === 'ads_strategy_day') {
        const title = getTitle({
          type: 'ads_strategy_day',
          current_time: data.current_time || '',
          date: data.result[data.job_id]?.date,
        });

        const modalData: DayStrategyModalData = {
          job_id: data.job_id,
          current_time: data.current_time,
          target_job_id: undefined,
          date: data.result[data.job_id]?.date,
        };

        openDayStrategyModal(modalData, title);
      } else if (data.job_id === 'ads_strategy_week') {
        const title = getTitle({
          type: 'ads_strategy_week',
          current_time: data.current_time || '',
          start_date: data.result[data.job_id]?.start_date,
          end_date: data.result[data.job_id]?.end_date,
          date: data.result[data.job_id]?.date,
          noDate: undefined
        });

        const modalData: WeekStrategyModalData = {
          job_id: data.job_id,
          current_time: data.current_time,
          target_job_id: undefined,
          date: data.result[data.job_id]?.date,
          isCompleteStrategy: undefined,
        };

        openWeekStrategyModal(modalData, title);
      } else if (data.job_id === 'market_report_week') {
        const title = getTitle({
          type: 'market_report_week',
          current_time: data.current_time || '',
          start_date: data.result[data.job_id]?.start_date,
          end_date: data.result[data.job_id]?.end_date,
          date: data.result[data.job_id]?.date,
        });

        const modalData: WeekMonthReportModalData = {
          job_id: data.job_id,
          current_time: data.current_time,
        };

        openWeekReportModal(modalData, title);
      } else if (data.job_id === 'market_report_month') {
        const title = getTitle({
          type: 'market_report_month',
          current_time: data.current_time || '',
          date: data.result[data.job_id]?.date,
          start_date: data.result[data.job_id]?.start_date,
          end_date: data.result[data.job_id]?.end_date,
        });

        const modalData: WeekMonthReportModalData = {
          job_id: data.job_id,
          current_time: data.current_time,
        };

        openMonthReportModal(modalData, title);
      }
    }
  }

  /** 点击定制内容 */
  const clickCur = (item: AIWorkItem) => {
    if (item.job_id === 'market_report_month') {
      const title = getTitle({
        type: 'market_report_month',
        current_time: item.current_time || '',
        date: item.date,
        start_date: item.result.market_report_month.start_date,
        end_date: item.result.market_report_month.end_date,
      });

      const modalData: WeekMonthReportModalData = {
        job_id: 'market_report_month',
        current_time: item.current_time,
      };

      openMonthReportModal(modalData, title);
    }
    if (item.job_id === 'ads_strategy_week') {
      const title = getTitle({
        type: 'ads_strategy_week',
        current_time: item.current_time || '',
        start_date: item.result.ads_strategy_week.start_date,
        end_date: item.result.ads_strategy_week.end_date,
        date: item.date,
        noDate: undefined
      });

      const modalData: WeekStrategyModalData = {
        job_id: 'ads_strategy_week',
        current_time: item.current_time,
        target_job_id: undefined,
        date: item.date,
        isCompleteStrategy: undefined,
      };

      openWeekStrategyModal(modalData, title);
    }

    if (item.job_id === 'ads_strategy_day') {
      const title = getTitle({
        type: 'ads_strategy_day',
        current_time: item.current_time || '',
        date: item.result.ads_strategy_day?.date,
      });

      const modalData: DayStrategyModalData = {
        job_id: 'ads_strategy_day',
        current_time: item.current_time,
        target_job_id: undefined,
        date: item.result.ads_strategy_day?.date,
      };

      openDayStrategyModal(modalData, title);
    }
  }


  const getWorkContent = (item: AIWorkItem) => {
    if (item.es_id.split('_').length > 1 && parseInt(item.es_id.split('_')[1]) > 1) {
      return <div>
        根据您的
        <span className={styles.highlight} onClick={() => { clickPrev(item) }}>修改意见</span>
        重新制定了
        <span className={styles.highlight} onClick={() => { clickCur(item) }}>{job_idMap[item.job_id]}</span>
        （{getShowDate(item)}）
      </div>
    }
    switch (item.job_id) {
      // 日广告投放策略
      case 'ads_strategy_day': {
        const ads_strategy_day = item.result.ads_strategy_day;
        if (!ads_strategy_day) return null
        const maxAdjustment = (ads_strategy_day.bid_adjustment_range?.max * 100).toFixed(0) + '%';
        const minAdjustment = (ads_strategy_day.bid_adjustment_range?.min * 100).toFixed(0) + '%';
        const approachDay = ads_strategy_day.approach === 'balanced' ? '平衡' : ads_strategy_day.approach === 'aggressive' ? '激进' : ads_strategy_day.approach === 'conservative' ? '保守' : ads_strategy_day.approach;
        return (
          <div>
            根据
            <span className={styles.highlight} onClick={() => {
              const title = getTitle({
                type: 'ads_strategy_week',
                current_time: item.current_time || '',
                start_date: undefined,
                end_date: undefined,
                date: undefined,
                noDate: true
              });

              const modalData: WeekStrategyModalData = {
                job_id: item.job_id,
                current_time: item.current_time,
                target_job_id: 'ads_strategy_week',
                date: undefined,
                isCompleteStrategy: undefined,
              };

              openWeekStrategyModal(modalData, title);
            }}
            >本周广告投放策略</span>
            制定了
            <span className={styles.highlight} onClick={() => {
              const title = getTitle({
                type: 'ads_strategy_day',
                current_time: '',
                date: ads_strategy_day.date,
              });

              const modalData: DayStrategyModalData = {
                job_id: 'ads_strategy_day',
                current_time: '',
                target_job_id: undefined,
                date: ads_strategy_day.date,
              };

              openDayStrategyModal(modalData, title);
            }}
            >明天广告投放策略</span>({ads_strategy_day.date} {getDayOfWeek(ads_strategy_day.date)})，
            计划采取<span className={styles.boldItem}>{approachDay}</span>策略，
            总预算为<span className={styles.boldItem}>{currency}{ads_strategy_day.day_budget.amount}</span>，
            竞价调整幅度为<span className={styles.boldItem}>{minAdjustment} ~ {maxAdjustment}</span>。
          </div>
        );
      }
      // 周广告投放策略
      case 'ads_strategy_week': {
        const ads_strategy_week = item.result.ads_strategy_week;
        if (!ads_strategy_week) return null
        const approachWeek = ads_strategy_week.approach === 'balanced' ? '平衡' : ads_strategy_week.approach === 'aggressive' ? '激进' : ads_strategy_week.approach === 'conservative' ? '保守' : ads_strategy_week.approach;
        return (
          <div>
            根据
            <span className={styles.highlight} onClick={() => {
              const title = getTitle({
                type: 'market_report_month',
                current_time: item.current_time || '',
                start_date: ads_strategy_week.start_date,
                end_date: ads_strategy_week.end_date,
                date: item.start_time,
              });

              const modalData: WeekMonthReportModalData = {
                job_id: 'ads_strategy_week',
                current_time: item.current_time,
                target_job_id: 'market_report_month',
              };

              openMonthReportModal(modalData, title);
            }}
            >月市场分析报告</span>
            制定了
            <span className={styles.highlight} onClick={() => {
              const title = getTitle({
                type: 'ads_strategy_week',
                current_time: item.current_time || '',
                start_date: ads_strategy_week.start_date,
                end_date: ads_strategy_week.end_date,
                date: item.date,
                noDate: undefined
              });

              const modalData: WeekStrategyModalData = {
                job_id: 'ads_strategy_week',
                current_time: item.current_time,
                target_job_id: undefined,
                date: item.date,
                isCompleteStrategy: undefined,
              };

              openWeekStrategyModal(modalData, title);
            }}
            >下周广告投放策略</span>({ads_strategy_week.start_date} ~ {ads_strategy_week.end_date})，
            计划采取<span className={styles.boldItem}>{approachWeek}</span>策略，
            主要目标为：<span className={styles.boldItem}>{ads_strategy_week.primary_goal.goal}</span>
          </div>
        );
      }
      // 周市场分析报告
      case 'market_report_week': {
        const market_report_week = item.result.market_report_week;
        if (!market_report_week) return null
        return (
          <div>
            完成了
            <span className={styles.highlight} onClick={() => {
              const title = getTitle({
                type: 'market_report_week',
                current_time: item.current_time || '',
                start_date: market_report_week?.start_date,
                end_date: market_report_week?.end_date,
                date: undefined,
              });

              const modalData: WeekMonthReportModalData = {
                job_id: 'market_report_week',
                current_time: item.current_time,
              };

              openWeekReportModal(modalData, title);
            }}
            >下周市场分析报告</span>({market_report_week?.start_date} ~ {market_report_week?.end_date})，
            {market_report_week?.forecast.market_preview.join(' ')}
          </div>
        );
      }
      // 市场分析师
      case 'market_report_month': {
        const market_report_month = item.result.market_report_month;
        if (!market_report_month) return null
        return (
          <div>
            完成了
            <span className={styles.highlight} onClick={() => {
              const title = getTitle({
                type: 'market_report_month',
                current_time: item.current_time || '',
                date: undefined,
                start_date: market_report_month.start_date,
                end_date: market_report_month.end_date,
              });

              const modalData: WeekMonthReportModalData = {
                job_id: item.job_id,
                current_time: item.current_time,
              };

              openMonthReportModal(modalData, title);
            }}
            >下月市场分析报告</span>({market_report_month.start_date} ~ {market_report_month.end_date})，
            {market_report_month.forecast.market_preview.join(' ')}
          </div>
        );
      }
      // 广告优化师
      case 'day_strategy_act': {
        // 获取预算调整和位置竞价的结果
        const campaignBudgetResult = item.result.set_campaign_budget?.set_campaign_budget || '';
        const placementBiddingResult = item.result.set_placement_bidding?.set_placement_bidding || '';
        const daypart_strategy = item.result.daypart_strategy;
        const ads_strategy_daypart = item.result.ads_strategy_daypart;
        // 格式化当前日期，使用current_time的日期部分或者今天的日期
        const displayDate = item.current_time ?
          dayjs(item.current_time).format('YYYY-MM-DD') :
          dayjs().format('YYYY-MM-DD');

        // 判断current_time是否为0点
        const isZeroHour = item.current_time ?
          dayjs(item.current_time).format('HH') === '00' :
          false;
        return (
          isZeroHour ?
            <div>
              根据
              <span className={styles.highlight} onClick={() => {
                const title = getTitle({
                  type: 'ads_strategy_day',
                  current_time: item.current_time || '',
                  date: displayDate,
                });

                const modalData: DayStrategyModalData = {
                  job_id: 'ads_strategy_day',
                  current_time: item.current_time,
                  target_job_id: 'ads_strategy_day',
                  date: displayDate,
                };

                openDayStrategyModal(modalData, title);
              }}
              >日广告投放策略</span>
              （{displayDate} {getDayOfWeek(displayDate)}）
              {campaignBudgetResult ? `，广告预算：${campaignBudgetResult}` : ''}
              {placementBiddingResult ? `，位置竞价：${placementBiddingResult}` : ''}
            </div>
            :
            <div>
              根据今天广告投放表现调整了
              <span className={styles.highlight} onClick={() => {
                const title = getTitle({
                  type: 'day_strategy_act',
                  current_time: item.current_time || '',
                });

                const modalData: DayStrategyCatModalData = {
                  job_id: 'day_strategy_act',
                  current_time: item.current_time,
                  target_job_id: undefined,
                };

                openDayStrategyCatModal(modalData, title);
              }}
              >广告优化调整</span>（{item.current_time}），
              {(daypart_strategy?.daily_adjustment_proposal?.overall_rationale || ads_strategy_daypart?.daily_adjustment_proposal?.overall_rationale)}
            </div>

        );
      }
      default:
        return null;
    }
  };

  return <div className={styles.container}>
    {/* AI工作动态板块 */}
    <Title level={3} className={styles.title}>AI 工作动态</Title>

    <Card data-test-id="aiwork-tabs" className="card">
      <div className={styles.aiWorkContainer}>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={(dates) => {
              setDateRange(dates as [dayjs.Dayjs | null, dayjs.Dayjs | null]);
              setPageNo(1);
            }}
          />
        </div>
        <Tabs
          activeKey={role}
          onChange={(key) => {
            setRole(key);
            setPageNo(1);
          }}
          className={styles.aiWorkTabs}
        >
          {(Object.keys(roleMap) as Array<keyof typeof roleMap>).map(key => (
            <TabPane tab={roleMap[key]} key={key} />
          ))}
        </Tabs>

        <div className={styles.aiWorkList}>
          {aiWorkData.map((item, index) => (
            <div key={index} className={styles.aiWorkItem}>
              {getAvatarByType(item.role)}
              <div className={styles.aiWorkContent}>
                <div className={styles.aiWorkRole}>
                  <span className={styles.aiWorkType}>{roleMap[item.role]}</span>
                  <span className={styles.aiWorkDesc}>{roleDescMap[item.role]}</span>
                </div>
                <div className={styles.aiWorkMainContent}>
                  {getWorkContent(item)}
                </div>
                <span className={styles.aiWorkTimestamp}>
                  {item.start_time_local}（{item.current_country.toLocaleUpperCase()}）
                  <br />
                  {item.bj_time}（北京）
                </span>
              </div>
            </div>
          ))}
        </div>

        {total > pageSize && (
          <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'flex-end' }}>
            <Pagination
              current={pageNo}
              pageSize={pageSize}
              total={total}
              onChange={(page, pageSize) => {
                setPageNo(page);
                setPageSize(pageSize);
              }}
              showSizeChanger
              showQuickJumper
              showTotal={(total) => `共 ${total} 条记录`}
            />
          </div>
        )}
      </div>
    </Card>


  </div>;
};

export default AiWork;