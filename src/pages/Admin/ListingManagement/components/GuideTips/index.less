// 顶部指南组件样式
.guideBox {
  position: relative;
  margin-bottom: 16px;
  background: #FFFFFF;
  border-radius: 8px;
}
.guideIcon {
  position: absolute;
  top: 12px;
  left: 16px;
  width: 20px;
  height: 20px;
}

.guideToggle {
  position: absolute;
  top: 6px;
  right: 0px;
  color: #4E5969;
  cursor: pointer;
  font-size: 13px;
  z-index: 1;
}

.guideContent {
  height: 40px;
  padding-left: 32px; // 与标题左侧对齐
  font-size: 13px;
  line-height: 1.6em;
  overflow: hidden;
  transition: height 0.3s ease-in-out;
}

.expanded {
  height:167px; // 设置具体的展开高度，可以根据实际内容调整
  overflow: hidden;
}

.ul{
  margin: 0;
  padding-left: 18px;
  li{
    list-style: disc;
  }
}